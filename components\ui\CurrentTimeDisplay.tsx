import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';

// Helper function to format date/time (Copied from AppFooter)
const formatDateTime = (date: Date) => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return {
        dateString: `${year}.${month}.${day}`,
        timeString: `${hours}:${minutes}:${seconds}`
    };
};

const CurrentTimeDisplayComponent: React.FC = () => {
    const [currentTime, setCurrentTime] = useState(new Date());

    useEffect(() => {
        const timerId = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000); // Update every second

        return () => {
            clearInterval(timerId); // Cleanup interval on unmount
        };
    }, []); // Run only once on mount

    const { dateString, timeString } = formatDateTime(currentTime);

    return (
        <View className="flex-row space-x-2">
             <Text className="text-aegis-cyan font-mono text-xs">{dateString}</Text>
             <Text className="text-aegis-cyan font-mono text-xs">{timeString}</Text>
        </View>
    );
};

// Memoize the component to prevent unnecessary re-renders
export const CurrentTimeDisplay = React.memo(CurrentTimeDisplayComponent);
