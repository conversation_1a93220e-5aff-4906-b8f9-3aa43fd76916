import React, { Suspense, useCallback, useState, useEffect } from "react";
import { View, Text, ScrollView, Pressable } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { useSystemInfoStore } from "../store/systemInfoStore";
import { StatSection } from "../components/sections/StatSection";
import { ScrollingLogSection } from "../components/sections/ScrollingLogSection";
import { StatLine } from "../components/ui/StatLine";
import { WifiIcon } from "../components/icons/WifiIcon";
import { CellularIcon } from "../components/icons/CellularIcon";
import { getConnectionQualityStatus } from "../lib/utils";
import { SignalStrengthVisualizer } from "../components/ui/SignalStrengthVisualizer";
import { LatencyHistoryChart } from "../components/charts/LatencyHistoryChart";
import { ScanlineOverlay } from "../components/layout/ScanlineOverlay";
import { GlitchOverlay } from "../components/layout/GlitchOverlay";
import { Network3dModel } from "../components/core/Network3dModel";
import { Canvas } from "@react-three/fiber/native";



export default function NetworkDetailScreen() {
  const {
    netInfo,
    networkLogMessages,
    addNetworkLogMessage,
    publicIpAddress,
    latencyMs,
    isTestingLatency,
    setLatencyMs,
    setIsTestingLatency,
    addSpeedTestResult,
  } = useSystemInfoStore();

  const type = netInfo?.type?.toUpperCase() ?? "UNKNOWN";
  const isConnected = netInfo?.isConnected;
  const isInternetReachable = netInfo?.isInternetReachable;
  const networkType = netInfo?.type ?? null;

  let ipAddress = "--";
  let ssid = "--";
  let strengthString = "--";
  let strengthValue: number | null = null;
  let frequency = "--";
  let carrier = "--";
  let generation = "--";
  let gatewayAddress = "--";
  let gatewayLabel = "GATEWAY ADDR";

  const isSubnetMask = (value: string | null | undefined): boolean => {
    if (!value) return false;
    const commonMasks = [
      "*************",
      "***********",
      "*********",
      "***************",
      "***************",
      "***************",
      "***************",
      "***************",
      "***************",
    ];
    return commonMasks.includes(value);
  };

  if (netInfo?.details) {
    if (netInfo.type === "wifi" || netInfo.type === "ethernet") {
      ipAddress = netInfo.details.ipAddress ?? "--";

      if (netInfo.details.subnet && isSubnetMask(netInfo.details.subnet)) {
        gatewayAddress = netInfo.details.subnet;
        gatewayLabel = "SUBNET MASK";
      } else if (netInfo.details.subnet) {
        gatewayAddress = netInfo.details.subnet;
        gatewayLabel = "GATEWAY ADDR";
      } else {
        gatewayAddress = "N/A";
        gatewayLabel = "GATEWAY";
      }
    }

    if (netInfo.type === "wifi") {
      ssid = netInfo.details.ssid ?? "--";
      strengthString = netInfo.details.strength
        ? `${netInfo.details.strength}%`
        : "--";
      strengthValue =
        typeof netInfo.details.strength === "number"
          ? netInfo.details.strength / 100
          : null;
      frequency = netInfo.details.frequency
        ? `${netInfo.details.frequency} MHz`
        : "--";
    } else if (netInfo.type === "cellular") {
      carrier = netInfo.details.carrier ?? "--";
      strengthValue = isConnected ? 0.7 : 0;
      generation = netInfo.details.cellularGeneration?.toUpperCase() ?? "--";
      gatewayAddress = "N/A";
      gatewayLabel = "GATEWAY";
    }
  }

  const quality = getConnectionQualityStatus(isConnected, isInternetReachable, strengthValue);


  const handleLatencyTest = useCallback(async () => {
    if (isTestingLatency) return;

    setIsTestingLatency(true);
    setLatencyMs(null);
    addNetworkLogMessage('> INITIATING LATENCY PROBE...');

    const targetUrl = 'https://www.google.com';
    const startTime = performance.now();
    let currentTestLatency: number | null = null;

    try {
      const response = await fetch(targetUrl, { method: 'HEAD', cache: 'no-store' });
      const endTime = performance.now();

      if (response.ok) {
        const durationMs = Math.round(endTime - startTime);
        currentTestLatency = durationMs;
        setLatencyMs(durationMs);
        addNetworkLogMessage(`> LATENCY PROBE (${targetUrl}): ${durationMs} ms`);
        addSpeedTestResult({ latency: durationMs });
      } else {
        throw new Error(`Latency test failed with status: ${response.status}`);
      }
    } catch (error: any) {
      if (__DEV__) { console.error("[LatencyTest] Error:", error); }
      addNetworkLogMessage(`> ERROR: LATENCY PROBE FAILED (${error.message || 'Unknown error'})`);
      setLatencyMs(null);
    } finally {
      setIsTestingLatency(false);
    }
  }, [isTestingLatency, setIsTestingLatency, setLatencyMs, addNetworkLogMessage, addSpeedTestResult]);

  const [isGlitching, setIsGlitching] = useState(false);

  useEffect(() => {
    setIsGlitching(true);
    const timer = setTimeout(() => setIsGlitching(false), 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-black">
      <Stack.Screen
        options={{
          headerShown: true,
          headerStyle: { backgroundColor: "#000000" },
          headerTintColor: "#00BFFF",
          headerTitleStyle: { fontFamily: "Orbitron-Bold", color: "#FF6600" },
          title: "NETWORK DETAIL",
          headerBackTitle: "Back",
        }}
      />
      <ScanlineOverlay />
      <GlitchOverlay isGlitching={isGlitching} />
      <View className="flex-1 p-3">
        <View className="flex-[0.6] mb-3 border border-aegis-cyan/20">
          <Suspense fallback={<Text>Loading...</Text>}>
            <Canvas
              camera={{ position: [0, 0, 4.5], fov: 50 }}
              gl={__DEV__ ? { debug: { checkShaderErrors: false, onShaderError: null } } : undefined}
              onCreated={({ gl }) => {
                gl.domElement.addEventListener('webglcontextlost', () => {
                  console.warn('Network Canvas: WebGL context lost');
                });
                gl.domElement.addEventListener('webglcontextrestored', () => {
                  console.warn('Network Canvas: WebGL context restored');
                });
              }}
            >
              <ambientLight intensity={0.5} />
              <directionalLight position={[3, 4, 5]} intensity={0.7} />
              <Network3dModel
                isConnected={isConnected}
                 networkType={networkType}
                 signalStrength={strengthValue}
                 latencyMs={latencyMs}
               />
            </Canvas>
          </Suspense>
        </View>

        <View className="flex-[0.4]">
          <ScrollView className="flex-1">
             <View className="mb-4">
                <ScrollingLogSection title="NETWORK LOG" lineHeight={20} logMessages={networkLogMessages} />
             </View>

             <StatSection title="NETWORK VECTOR">
               <StatLine
                 label="STATUS"
                 value={quality.status}
                 valueGlow={quality.color}
                 valueColor={quality.color === 'red' ? 'text-aegis-red' : quality.color === 'orange' ? 'text-aegis-orange' : 'text-aegis-cyan'}
               />
               <StatLine
                 label="Type"
              value={type}
              icon={
                netInfo?.type === "wifi" ? (
                  <WifiIcon />
                ) : netInfo?.type === "cellular" ? (
                  <CellularIcon />
                ) : undefined
              }
              valueGlow="cyan"
            />
            <StatLine
              label="LINK STATUS"
              value={isConnected === null ? "--" : isConnected ? "ESTABLISHED" : "SEVERED"}
              valueGlow={isConnected ? "cyan" : "red"}
            />
            <StatLine
              label="WAN ACCESS"
              value={
                isInternetReachable === null
                  ? "--"
                  : isInternetReachable
                  ? "REACHABLE"
                  : "UNREACHABLE"
              }
              valueColor={
                isInternetReachable === false
                  ? "text-aegis-red"
                  : "text-aegis-cyan"
              }
              valueGlow={isInternetReachable ? "cyan" : "red"}
            />
            <StatLine label="INTERNAL ADDR" value={ipAddress} valueGlow="cyan" />
            <StatLine label="EXTERNAL ADDR" value={publicIpAddress ?? '--'} valueGlow={publicIpAddress && publicIpAddress !== 'ERROR' ? "cyan" : "red"} />
            <StatLine label={gatewayLabel} value={gatewayAddress} valueGlow="cyan" />
            {netInfo?.type === "wifi" && (
              <>
                <StatLine label="WLAN ID" value={ssid} valueGlow="cyan" />
                <StatLine
                  label="SIGNAL STR"
                  valueComponent={<SignalStrengthVisualizer strengthValue={strengthValue} />}
                />
                <StatLine
                  label="WLAN FREQ"
                  value={frequency}
                  valueGlow="cyan"
                />
              </>
            )}
            {netInfo?.type === "cellular" && (
              <>
                <StatLine label="PROVIDER" value={carrier} valueGlow="cyan" />
                <StatLine
                  label="CELL GEN"
                  value={generation}
                  valueGlow="cyan"
                />
              </>
            )}
              <StatLine
                label="LATENCY PROBE"
                value={
                  isTestingLatency
                    ? "TESTING..."
                    : latencyMs !== null
                    ? `${latencyMs} ms`
                    : "-- ms"
                }
                valueGlow={latencyMs !== null ? (latencyMs < 100 ? 'cyan' : latencyMs < 300 ? 'orange' : 'red') : undefined}
              />
            </StatSection>

            <View className="mt-4 flex-row justify-center space-x-4">

              <Pressable
                onPress={handleLatencyTest}
                disabled={isTestingLatency}
                className={`py-2 px-5 border border-aegis-cyan/70 rounded ${
                  isTestingLatency
                    ? "border-aegis-red/50 bg-black opacity-50"
                    : "active:bg-aegis-cyan/10 active:border-aegis-cyan"
                }`}
              >
                <Text
                   className={`font-orbitron text-sm text-center ${
                    isTestingLatency ? "text-aegis-red/70" : "text-aegis-cyan text-shadow-glow-cyan"
                   }`}
                >
                  {isTestingLatency ? "TESTING..." : "LATENCY TEST"}
                </Text>
              </Pressable>
            </View>

            <View className="mt-4 pb-12">
              <LatencyHistoryChart height={160} maxHistoryItems={7} />
            </View>
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
}
