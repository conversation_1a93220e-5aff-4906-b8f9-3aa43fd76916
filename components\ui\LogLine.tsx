import React, { useEffect, useRef } from 'react';
import { Text, Animated } from 'react-native';

interface LogLineProps {
  message: string;
  isError?: boolean; // Flag for error styling
}

export const LogLine = ({ message, isError = false }: LogLineProps) => {
  const flashAnim = useRef(new Animated.Value(0)).current;
  const textColor = isError ? 'text-aegis-red' : 'text-aegis-cyan'; // Red for error, cyan otherwise

  useEffect(() => {
    if (isError) {
      // Flash animation for errors
      Animated.loop(
        Animated.sequence([
          Animated.timing(flashAnim, { toValue: 1, duration: 400, useNativeDriver: false }),
          Animated.timing(flashAnim, { toValue: 0.5, duration: 400, useNativeDriver: false }), // Dim slightly
        ]),
        { iterations: -1 } // Loop indefinitely while it's an error
      ).start();
    } else {
      flashAnim.stopAnimation();
      flashAnim.setValue(1); // Default to full opacity if not error
    }
    return () => flashAnim.stopAnimation();
  }, [isError, flashAnim]);

  // Interpolate opacity for flashing effect
  const animatedOpacity = flashAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.5, 1] // Adjust opacity range for flash
  });

  return (
    <Animated.Text
      className={`font-mono text-xs ${textColor}`}
      style={isError ? { opacity: animatedOpacity } : {}}
      selectable={true}
    >
      {message}
    </Animated.Text>
  );
};
