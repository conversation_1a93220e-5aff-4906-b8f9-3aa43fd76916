{"expo": {"name": "Aegis System Monitor", "slug": "aegis-system-monitor", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "com.aegis.systemmonitor", "userInterfaceStyle": "dark", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSUserTrackingUsageDescription": "This identifier will be used to deliver personalized ads to you.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "cstr6suwn9.skadnetwork"}, {"SKAdNetworkIdentifier": "cstr6suwn9.skadnetwork"}], "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.aegis.systemmonitor"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.aegis.systemmonitor", "permissions": ["INTERNET", "VIBRATE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "READ_MEDIA_AUDIO"], "blockedPermissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "SYSTEM_ALERT_WINDOW"], "versionCode": 1}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#000000"}], "expo-font", ["expo-build-properties", {"android": {"kotlinVersion": "1.9.25", "composeCompilerVersion": "1.5.15", "compileSdkVersion": 35, "targetSdkVersion": 34, "minSdkVersion": 24, "buildToolsVersion": "35.0.0", "packagingOptions": {"pickFirst": ["lib/x86/libjsi.so", "lib/x86_64/libjsi.so", "lib/armeabi-v7a/libjsi.so", "lib/arm64-v8a/libjsi.so"]}}, "ios": {"deploymentTarget": "15.1"}}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-1726497308447618~7438713728"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "72670009-f398-4522-b18f-0283f49c0839"}}, "updates": {"enabled": false}}}