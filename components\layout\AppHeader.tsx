import React, { useState, useEffect, useRef } from "react";
import { View, Text, Pressable } from "react-native";
import { Link } from "expo-router";
import colors from "tailwindcss/colors"; // Import Tailwind colors

// TODO: Consider moving time/date related helpers to lib/utils.ts or lib/timeUtils.ts if reused elsewhere
const formatDuration = (ms: number): string => {
  if (ms < 0) ms = 0; // Ensure non-negative duration
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  }
};

// Helper function for Paradigm Shift countdown
const getParadigmShiftInfo = (
  now: Date
): { minutesRemaining: string; shiftName: string } => {
  const currentHour = now.getHours();
  let endHour: number;
  let shiftName: string;

  if (currentHour < 8) {
    // 00:00 - 07:59
    endHour = 8;
    shiftName = "HORIZON"; // Shift happening at 8 AM
  } else if (currentHour < 16) {
    // 08:00 - 15:59
    endHour = 16;
    shiftName = "NOCTURNE"; // Shift happening at 4 PM (16:00)
  } else {
    // 16:00 - 23:59
    endHour = 24; // Midnight target
    shiftName = "MIDNIGHT"; // Shift happening at Midnight
  }

  const targetTime = new Date(now);
  targetTime.setHours(endHour, 0, 0, 0);

  const msRemaining = targetTime.getTime() - now.getTime();
  const minutesRemaining = Math.floor(msRemaining / (60 * 1000));

  return {
    minutesRemaining: minutesRemaining.toString(),
    shiftName: shiftName,
  };
};

// Removed orientationMap

interface AppHeaderProps {
  title?: string; // Optional title prop
}

export const AppHeader: React.FC<AppHeaderProps> = ({ title }) => {
  const startTimeRef = useRef(Date.now());
  // Removed orientation state
  const [sessionUptime, setSessionUptime] = useState("00:00");
  const [paradigmInfo, setParadigmInfo] = useState({
    minutesRemaining: "--",
    shiftName: "PARADIGM",
  });

  // Effect for Uptime (updates every second) and Countdown Timer (updates every minute)
  useEffect(() => {
    // Update uptime every second
    const uptimeIntervalId = setInterval(() => {
      setSessionUptime(formatDuration(Date.now() - startTimeRef.current));
    }, 1000);

    // Function to update paradigm shift info
    const updateParadigmShift = () => {
      const now = new Date();
      setParadigmInfo(getParadigmShiftInfo(now));
    };

    // Update paradigm shift immediately and then every minute
    updateParadigmShift();
    const paradigmIntervalId = setInterval(updateParadigmShift, 60000); // Update every minute

    // Initial uptime calculation
    setSessionUptime(formatDuration(Date.now() - startTimeRef.current));

    // Cleanup function
    return () => {
      clearInterval(uptimeIntervalId);
      clearInterval(paradigmIntervalId);
    };
  }, []); // Empty dependency array ensures this runs only once on mount

  // Removed Effect for screen orientation

  // Define colors based on availability
  const availableColor = colors.orange[500]; // Use orange when available
  const unavailableColor = colors.gray[600]; // Use gray when unavailable
  // TODO: Add flashing animation via className when available

  return (
    <View className="h-8 px-4 flex-row justify-between items-center border-b border-aegis-orange/50">
      <View className="flex-row items-center gap-2">
        <Text
          className="text-aegis-orange font-title text-base uppercase"
          style={{ textShadowColor: "#FF6600", textShadowRadius: 5 }}
        >
          {title || "AEGIS"}
        </Text>
      </View>

      <View className="flex-col items-center">
        <Text className="text-aegis-cyan font-mono text-xs">
          STATUS: NORMAL
        </Text>
        <Text className="text-aegis-cyan font-mono text-xs">
          UPTIME: {sessionUptime}
        </Text>
      </View>
      <View>
        <Text className="text-aegis-cyan font-mono text-xs text-right">
          T-{paradigmInfo.minutesRemaining} MIN
        </Text>
        <Text className="text-aegis-cyan/70 font-mono text-[10px] text-right -mt-1">
          UNTIL {paradigmInfo.shiftName} PARADIGM SHIFT
        </Text>
      </View>
    </View>
  );
};
