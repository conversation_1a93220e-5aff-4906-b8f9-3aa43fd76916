import React, { useMemo, useState } from 'react';
import { View } from 'react-native';
import Svg, { Path, Line, Text as SvgText, Defs, LinearGradient, Stop } from 'react-native-svg';

interface BatteryHistoryEntry {
  timestamp: number;
  level: number;
}

interface BatteryChartProps {
  data: BatteryHistoryEntry[];
  width?: number;
  height?: number;
  axisColor?: string;
  axisLabelColor?: string;
  timeframe: '1h' | '6h' | '24h';
}

const COLOR_CYAN = "#00BFFF";
const COLOR_ORANGE = "#FF7F00";
const COLOR_RED = "#FF0000";

const CHART_PADDING = 10;
const AXIS_LABEL_WIDTH = 30;

export const BatteryChart = ({
  data,
  height = 80,
  axisColor = "rgba(0, 191, 255, 0.3)",
  axisLabelColor = "rgba(0, 191, 255, 0.5)",
  timeframe,
}: BatteryChartProps) => {

  const [chartWidth, setChartWidth] = useState(100);

  const handleLayout = (event: any) => {
    setChartWidth(event.nativeEvent.layout.width);
  };

  const filteredData = useMemo(() => {
    const now = Date.now();
    let cutoffTimestamp: number;
    switch (timeframe) {
        case '6h': cutoffTimestamp = now - 6 * 60 * 60 * 1000; break;
        case '24h': cutoffTimestamp = now - 24 * 60 * 60 * 1000; break;
        case '1h':
        default: cutoffTimestamp = now - 1 * 60 * 60 * 1000; break;
    }
    const firstVisibleIndex = data.findIndex(entry => entry.timestamp >= cutoffTimestamp);
    if (firstVisibleIndex === -1 && data.length > 0) return data.slice(-2);
    if (firstVisibleIndex === -1 && data.length === 0) return [];
    if (firstVisibleIndex === 0) return data;
    return data.slice(firstVisibleIndex - 1);

  }, [data, timeframe]);


  const pathSegments = useMemo(() => {
    if (!filteredData || filteredData.length < 2 || chartWidth <= AXIS_LABEL_WIDTH + CHART_PADDING * 2) return [];

    const segments: { linePath: string; fillPath: string; color: string }[] = [];
    let currentSegmentPoints: {x: number, y: number}[] = [];
    let currentSegmentColor: string = COLOR_CYAN;

    const availableWidth = chartWidth - AXIS_LABEL_WIDTH - CHART_PADDING * 2;
    const availableHeight = height - CHART_PADDING * 2;
    const bottomY = height - CHART_PADDING;
    const minTimestamp = filteredData[0].timestamp;
    const maxTimestamp = filteredData[filteredData.length - 1].timestamp;
    const timeRange = maxTimestamp - minTimestamp || 1;

    const getColorForLevel = (level: number): string => {
        if (level < 0.2) return COLOR_RED;
        if (level < 0.5) return COLOR_ORANGE;
        return COLOR_CYAN;
    };

    const createPaths = (points: {x: number, y: number}[], color: string) => {
        if (points.length < 2) return;
        const linePointsStr = points.map(p => `${p.x.toFixed(1)},${p.y.toFixed(1)}`);
        const linePath = `M${linePointsStr.join(' L')}`;
        const startX = points[0].x;
        const endX = points[points.length - 1].x;
        const fillPath = `M${startX.toFixed(1)},${bottomY} L${linePointsStr.join(' L')} L${endX.toFixed(1)},${bottomY} Z`;
        segments.push({ linePath, fillPath, color });
    };

    for (let i = 0; i < filteredData.length; i++) {
        const entry = filteredData[i];
        const x = CHART_PADDING + AXIS_LABEL_WIDTH + ((entry.timestamp - minTimestamp) / timeRange) * availableWidth;
        const y = CHART_PADDING + availableHeight - (entry.level * availableHeight);
        const point = { x, y };
        const pointColor = getColorForLevel(entry.level);

        if (i === 0) {
            currentSegmentPoints.push(point);
            currentSegmentColor = pointColor;
        } else {
            const prevPoint = currentSegmentPoints[currentSegmentPoints.length - 1];
            const prevEntry = filteredData[i-1];
            const prevPointColor = getColorForLevel(prevEntry.level);

            if (pointColor !== prevPointColor) {
                let thresholdLevel: number | null = null;
                if ((prevPointColor === COLOR_CYAN && pointColor === COLOR_ORANGE) || (prevPointColor === COLOR_ORANGE && pointColor === COLOR_CYAN)) thresholdLevel = 0.5;
                else if ((prevPointColor === COLOR_ORANGE && pointColor === COLOR_RED) || (prevPointColor === COLOR_RED && pointColor === COLOR_ORANGE)) thresholdLevel = 0.2;

                if (thresholdLevel !== null) {
                    const levelDiff = entry.level - prevEntry.level;
                    const timeDiff = entry.timestamp - prevEntry.timestamp;
                    if (Math.abs(levelDiff) > 1e-6 && timeDiff > 0) {
                         const ratio = (thresholdLevel - prevEntry.level) / levelDiff;
                         const thresholdTimestamp = prevEntry.timestamp + timeDiff * ratio;
                         const thresholdX = CHART_PADDING + AXIS_LABEL_WIDTH + ((thresholdTimestamp - minTimestamp) / timeRange) * availableWidth;
                         const thresholdY = CHART_PADDING + availableHeight - (thresholdLevel * availableHeight);
                         const interpolatedPoint = { x: thresholdX, y: thresholdY };

                         currentSegmentPoints.push(interpolatedPoint);
                         createPaths(currentSegmentPoints, prevPointColor);
                         currentSegmentPoints = [interpolatedPoint, point];
                         currentSegmentColor = pointColor;
                         continue;
                    }
                }
                createPaths(currentSegmentPoints, prevPointColor);
                currentSegmentPoints = [prevPoint, point];
                currentSegmentColor = pointColor;
            } else {
                currentSegmentPoints.push(point);
            }
        }
    }
    createPaths(currentSegmentPoints, currentSegmentColor);

    return segments;

  }, [filteredData, chartWidth, height]);

      
  const yAxisElements = useMemo(() => {
    const labels = [100, 50, 0];
    const availableHeight = height - CHART_PADDING * 2;
    return labels.map(label => {
      const y = CHART_PADDING + availableHeight - ((label / 100) * availableHeight);
      return (
        <React.Fragment key={`y-axis-${label}`}>
          <SvgText
            x={CHART_PADDING + AXIS_LABEL_WIDTH - 5}
            y={y + 4}
            fill={axisLabelColor}
            fontSize="10"
            textAnchor="end"
            fontFamily="SpaceMono-Regular"
          >
            {label}%
          </SvgText>
          <Line
            x1={CHART_PADDING + AXIS_LABEL_WIDTH}
            y1={y}
            x2={chartWidth - CHART_PADDING}
            y2={y}
            stroke={axisColor}
            strokeWidth={0.5}
            strokeDasharray="2 2"
          />
        </React.Fragment>
      );
    });
  }, [height, chartWidth, axisColor, axisLabelColor]);


  return (
    <View style={{ height: height, width: '100%' }} onLayout={handleLayout}>
      <Svg height={height} width={chartWidth}>
        {yAxisElements}

         <Line
            x1={CHART_PADDING + AXIS_LABEL_WIDTH}
            y1={CHART_PADDING}
            x2={CHART_PADDING + AXIS_LABEL_WIDTH}
            y2={height - CHART_PADDING}
            stroke={axisColor}
            strokeWidth={0.5}
          />

        <Defs>
          <LinearGradient id="grad-cyan" x1="0" y1="0" x2="0" y2="1">
            <Stop offset="0" stopColor={COLOR_CYAN} stopOpacity="0.3" />
            <Stop offset="1" stopColor={COLOR_CYAN} stopOpacity="0.05" />
          </LinearGradient>
           <LinearGradient id="grad-orange" x1="0" y1="0" x2="0" y2="1">
            <Stop offset="0" stopColor={COLOR_ORANGE} stopOpacity="0.3" />
            <Stop offset="1" stopColor={COLOR_ORANGE} stopOpacity="0.05" />
          </LinearGradient>
           <LinearGradient id="grad-red" x1="0" y1="0" x2="0" y2="1">
            <Stop offset="0" stopColor={COLOR_RED} stopOpacity="0.3" />
            <Stop offset="1" stopColor={COLOR_RED} stopOpacity="0.05" />
          </LinearGradient>
        </Defs>

        {pathSegments.map((segment, index) => {
            const gradientId = segment.color === COLOR_RED ? 'url(#grad-red)' : segment.color === COLOR_ORANGE ? 'url(#grad-orange)' : 'url(#grad-cyan)';
            return (
                <React.Fragment key={index}>
                    <Path d={segment.fillPath} fill={gradientId} stroke="none" />
                    <Path d={segment.linePath} stroke={segment.color} strokeWidth={1.5} fill="none" />
                </React.Fragment>
            )
        })}
      </Svg>
    </View>
  );
};
