import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  LayoutChangeEvent,
  DimensionValue,
} from 'react-native';
import Svg, { Path, Line, Text as SvgText, Circle } from 'react-native-svg';
import { useSystemInfoStore } from '../../store/systemInfoStore';
import { formatRelativeTimeThematic } from '../../lib/utils';

interface SpeedTestHistoryEntry {
  timestamp: number;
  latency: number | null;
}

interface SpeedTestHistoryChartProps {
  width?: DimensionValue;
  height?: number;
  maxHistoryItems?: number;
}

const AXIS_COLOR = '#00BFFF';
const LATENCY_COLOR = '#FFFFFF';
const TEXT_COLOR = '#FFFFFF';
const CHART_PADDING = 10;
const Y_AXIS_LABEL_WIDTH = 30;
const LATENCY_Y_AXIS_LABEL_WIDTH = 30;
const X_AXIS_LABEL_HEIGHT = 15;
const TEXT_Y_OFFSET = 4;
const MIN_CHARTABLE_WIDTH = Y_AXIS_LABEL_WIDTH + LATENCY_Y_AXIS_LABEL_WIDTH + CHART_PADDING * 2 + 20;

const getNiceAxisMax = (maxValue: number, minDefault: number = 10): number => {
    if (maxValue <= 0) return minDefault;
    const paddedMax = maxValue * 1.1;
    if (paddedMax <= 10) return 10;
    if (paddedMax <= 50) return 50;
    if (paddedMax <= 100) return 100;
    if (paddedMax <= 500) return 500;
    return Math.ceil(paddedMax / 100) * 100;
};

export const LatencyHistoryChart = ({
  width: propWidth,
  height = 80,
  maxHistoryItems = 7,
}: SpeedTestHistoryChartProps) => {
  const history = useSystemInfoStore((state) => state.speedTestHistory);
  const [measuredWidth, setMeasuredWidth] = useState(0);

  const chartWidthForCalculations = typeof propWidth === 'number' && propWidth > 0 ? propWidth : measuredWidth;

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width: newWidth } = event.nativeEvent.layout;
    if (typeof propWidth !== 'number' || propWidth <= 0) {
        setMeasuredWidth(newWidth);
    }
  };

  const recentHistory = useMemo(() => history.slice(-maxHistoryItems), [history, maxHistoryItems]);

  const timeMeta = useMemo(() => {
    if (!recentHistory || recentHistory.length < 1) {
      return { minTimestamp: 0, maxTimestamp: 0, timeRange: 1, hasData: false };
    }
    const timestamps = recentHistory.map(item => item.timestamp);
    const minTimestamp = Math.min(...timestamps);
    const maxTimestamp = Math.max(...timestamps);
    return {
      minTimestamp,
      maxTimestamp,
      timeRange: Math.max(1, maxTimestamp - minTimestamp),
      hasData: true,
    };
  }, [recentHistory]);

  const latencyMeta = useMemo(() => {
    if (!recentHistory || recentHistory.length === 0) {
      return { maxLatency: 100 };
    }
    const latencies = recentHistory.map(item => item.latency ?? 0).filter(l => l > 0);
    const maxL = latencies.length > 0 ? Math.max(...latencies) : 0;
    return {
      maxLatency: getNiceAxisMax(maxL, 100),
    };
  }, [recentHistory]);

  const layoutMetrics = useMemo(() => {
    const chartAreaXStart = CHART_PADDING + Y_AXIS_LABEL_WIDTH;
    const chartAreaXEnd = chartWidthForCalculations - CHART_PADDING - LATENCY_Y_AXIS_LABEL_WIDTH;
    const availableWidth = Math.max(0, chartAreaXEnd - chartAreaXStart);

    const chartAreaYStart = CHART_PADDING;
    const chartAreaYEnd = height - CHART_PADDING - X_AXIS_LABEL_HEIGHT;
    const availableHeight = Math.max(0, chartAreaYEnd - chartAreaYStart);

    return {
      chartAreaXStart,
      chartAreaXEnd,
      availableWidth,
      chartAreaYStart,
      chartAreaYEnd,
      availableHeight,
      isValid: chartWidthForCalculations > MIN_CHARTABLE_WIDTH && height > (CHART_PADDING * 2 + X_AXIS_LABEL_HEIGHT + 20) && availableWidth > 0 && availableHeight > 0,
    };
  }, [chartWidthForCalculations, height]);

  const scaleLatencyY = (value: number | null): number => {
    const { chartAreaYStart, availableHeight, chartAreaYEnd } = layoutMetrics;
    const { maxLatency } = latencyMeta;
    if (value === null || !Number.isFinite(value) || value <= 0 || maxLatency <= 0 || availableHeight <= 0) {
      return chartAreaYEnd;
    }
    const scaled = (value / maxLatency) * availableHeight;
    return chartAreaYStart + availableHeight - Math.max(0, Math.min(scaled, availableHeight));
  };

  const scaleTimestampX = (timestamp: number, index: number, totalPoints: number): number => {
    const { chartAreaXStart, availableWidth } = layoutMetrics;
          const { minTimestamp, timeRange } = timeMeta;
      if (__DEV__) {
        console.log('--- DEBUG START ---');
        console.log('Prop maxHistoryItems:', maxHistoryItems);
        console.log('Raw history from store (first 10):', JSON.stringify(history.slice(0, 10), null, 2));
        console.log('RECENT HISTORY for pointData:', JSON.stringify(recentHistory, null, 2));
        console.log('RECENT HISTORY length:', recentHistory.length);
      }
    if (totalPoints <= 0 || availableWidth <= 0) return chartAreaXStart;
    if (totalPoints === 1) return chartAreaXStart + availableWidth / 2;

    if (timeRange <= 1 && totalPoints > 1) {
        return chartAreaXStart + (index / (totalPoints - 1)) * availableWidth;
    }

    const relativePosition = (timestamp - minTimestamp) / timeRange;
    return chartAreaXStart + Math.max(0, Math.min(1, relativePosition)) * availableWidth;
  };

  const pointData = useMemo(() => {
    type Point = { x: number; y: number };
    const path: string[] = [];
    const points: Point[] = [];

    if (!timeMeta.hasData || !layoutMetrics.isValid || recentHistory.length === 0) {
      return { latencyPath: '', latencyPoints: [] };
    }

    let firstPoint = true;
    recentHistory.forEach((item, index) => {
      const x = scaleTimestampX(item.timestamp, index, recentHistory.length);
      if (item.latency !== null && Number.isFinite(item.latency)) {
        const y = scaleLatencyY(item.latency);
        if (Number.isFinite(x) && Number.isFinite(y)) {
          points.push({ x, y });
          if (recentHistory.length >= 1) {
            const command = firstPoint ? 'M' : 'L';
            path.push(`${command}${x.toFixed(1)},${y.toFixed(1)}`);
            firstPoint = false;
          }
        }
      }
    });
    return {
      latencyPath: path.length > 1 ? path.join(' ') : '',
      latencyPoints: points,
    };
  }, [recentHistory, layoutMetrics, timeMeta, latencyMeta]);

  const yAxisLatencyElements = useMemo(() => {
    if (!layoutMetrics.isValid) return null;

    const { maxLatency } = latencyMeta;
    const { chartAreaYStart, availableHeight, chartAreaYEnd } = layoutMetrics;
    const numTicks = 5;
    const ticks = Array.from({ length: numTicks }, (_, i) => maxLatency * (i / (numTicks - 1)));

    return ticks.map((labelValue) => {
      if (!Number.isFinite(labelValue) || maxLatency <= 0 || availableHeight <= 0) return null;
      const y = chartAreaYStart + availableHeight - ((labelValue / maxLatency) * availableHeight);
      if (y < chartAreaYStart - TEXT_Y_OFFSET || y > chartAreaYEnd + TEXT_Y_OFFSET*2) return null;

      return (
        <SvgText
          key={`y-latency-axis-${labelValue}`}
          x={chartWidthForCalculations - CHART_PADDING - LATENCY_Y_AXIS_LABEL_WIDTH + 5} // Use chartWidthForCalculations
          y={y + TEXT_Y_OFFSET}
          fill={LATENCY_COLOR}
          fontSize="8"
          textAnchor="start"
          fontFamily="SpaceMono-Regular"
        >
          {labelValue.toFixed(0)} ms
        </SvgText>
      );
    });
  }, [layoutMetrics, latencyMeta, chartWidthForCalculations]);

  const xAxisTimeElements = useMemo(() => {
    if (!timeMeta.hasData || !layoutMetrics.isValid || recentHistory.length === 0) return null;

    const { minTimestamp, maxTimestamp } = timeMeta;
    const { chartAreaYEnd } = layoutMetrics;

    const firstX = scaleTimestampX(minTimestamp, 0, recentHistory.length);
    const lastX = scaleTimestampX(maxTimestamp, recentHistory.length - 1, recentHistory.length);
    const yPos = chartAreaYEnd + X_AXIS_LABEL_HEIGHT / 2 + TEXT_Y_OFFSET;

    return (
      <>
        <SvgText x={firstX} y={yPos} fill={TEXT_COLOR} fontSize="8" textAnchor="middle" fontFamily="SpaceMono-Regular">
          {formatRelativeTimeThematic(minTimestamp)}
        </SvgText>
        {recentHistory.length > 1 && minTimestamp !== maxTimestamp && (
          <SvgText x={lastX} y={yPos} fill={TEXT_COLOR} fontSize="8" textAnchor="middle" fontFamily="SpaceMono-Regular">
            {formatRelativeTimeThematic(maxTimestamp)}
          </SvgText>
        )}
      </>
    );
  }, [recentHistory, layoutMetrics, timeMeta]);

  if (chartWidthForCalculations < MIN_CHARTABLE_WIDTH && (typeof propWidth !== 'number' || propWidth <=0) ) {
    return (
      <View
        style={{ width: propWidth ?? '100%', height }}
        onLayout={handleLayout}
      />
    );
  }

  if (!timeMeta.hasData) {
    return (
      <View style={{ width: propWidth ?? chartWidthForCalculations, height }} className="items-center justify-center mt-2" onLayout={handleLayout}>
        <Text className="text-aegis-cyan/50 text-xs font-mono">NO LATENCY HISTORY</Text>
      </View>
    );
  }

  if (!layoutMetrics.isValid) {
     return (
      <View style={{ width: propWidth ?? chartWidthForCalculations, height }} className="items-center justify-center mt-2" onLayout={handleLayout}>
        <Text className="text-aegis-cyan/50 text-xs font-mono">Chart too small</Text>
      </View>
    );
  }

  return (
    <View
      style={{ width: propWidth ?? chartWidthForCalculations }}
      className="mt-2"
      onLayout={handleLayout}
    >
      <View className="flex-row justify-between items-center mb-1 px-1" style={{ marginLeft: Y_AXIS_LABEL_WIDTH, marginRight: LATENCY_Y_AXIS_LABEL_WIDTH }}>
        <Text className="text-aegis-cyan font-mono text-xs uppercase">LATENCY HISTORY</Text>
      </View>

      <Svg height={height} width={chartWidthForCalculations}>
        {yAxisLatencyElements}
        <Line
            x1={layoutMetrics.chartAreaXStart} y1={layoutMetrics.chartAreaYStart}
            x2={layoutMetrics.chartAreaXStart} y2={layoutMetrics.chartAreaYEnd}
            stroke={AXIS_COLOR} strokeWidth={0.5}
        />
        <Line
            x1={layoutMetrics.chartAreaXStart} y1={layoutMetrics.chartAreaYEnd}
            x2={layoutMetrics.chartAreaXEnd} y2={layoutMetrics.chartAreaYEnd}
            stroke={AXIS_COLOR} strokeWidth={0.5}
        />
        <Line
            x1={layoutMetrics.chartAreaXEnd} y1={layoutMetrics.chartAreaYStart}
            x2={layoutMetrics.chartAreaXEnd} y2={layoutMetrics.chartAreaYEnd}
            stroke={LATENCY_COLOR} strokeOpacity={0.5} strokeWidth={0.5}
        />
        {xAxisTimeElements}
        {!!pointData.latencyPath && (
          <Path d={pointData.latencyPath} stroke={LATENCY_COLOR} strokeWidth={1} fill="none" strokeDasharray="3 3" />
        )}
        {pointData.latencyPoints.map((point, index) => (
          <Circle key={`l-dot-${index}-${point.x}-${point.y}`} cx={point.x} cy={point.y} r="1.5" fill={LATENCY_COLOR} />
        ))}
      </Svg>
    </View>
  );
};