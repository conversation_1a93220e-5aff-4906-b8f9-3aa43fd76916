import React from 'react';
import { View } from 'react-native';

interface SignalStrengthVisualizerProps {
  strengthValue: number | null; // Expecting 0-1, or null if unknown
  barCount?: number;
  maxHeight?: number;
  barWidth?: number;
  gap?: number;
}

export const SignalStrengthVisualizer = ({
  strengthValue,
  barCount = 4, // Common number of bars
  maxHeight = 12, // Max height in pixels
  barWidth = 3,
  gap = 1.5,
}: SignalStrengthVisualizerProps) => {
  const bars = [];
  const totalWidth = barCount * barWidth + (barCount - 1) * gap;

  // Determine how many bars should be 'active' based on strength
  let activeBars = 0;
  if (strengthValue !== null) {
    activeBars = Math.ceil(strengthValue * barCount);
  }

  for (let i = 0; i < barCount; i++) {
    // Calculate height based on bar index (increasing height)
    const barHeight = maxHeight * ((i + 1) / barCount);
    // Determine color based on whether the bar is active and overall strength
    let barColor = 'bg-aegis-cyan/30'; // Default inactive color
    if (i < activeBars) {
      if (strengthValue === null) { // Unknown but connected? Default cyan
        barColor = 'bg-aegis-cyan';
      } else if (strengthValue >= 0.7) {
        barColor = 'bg-aegis-cyan'; // Strong
      } else if (strengthValue >= 0.4) {
        barColor = 'bg-aegis-cyan'; // Medium (still cyan)
      } else if (strengthValue >= 0.1) {
        barColor = 'bg-aegis-orange'; // Weak
      } else {
        barColor = 'bg-aegis-red'; // Very Weak / Critical
      }
    }

    bars.push(
      <View
        key={i}
        className={`rounded-sm ${barColor}`}
        style={{
          width: barWidth,
          height: barHeight,
          marginLeft: i > 0 ? gap : 0,
        }}
      />
    );
  }

  return (
    <View className="flex-row items-end" style={{ width: totalWidth, height: maxHeight }}>
      {bars}
    </View>
  );
};
