import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from 'react-native';
import { generateFlicker } from '../../lib/utils'; // Adjusted import path

type GlowColor = 'orange' | 'cyan' | 'red';

interface StatLineProps {
  label: string;
  value?: string | undefined | null; // Make value optional if component is used
  valueComponent?: React.ReactNode; // New prop for custom component
  valueColor?: string;
  icon?: React.ReactNode;
  valueGlow?: GlowColor;
}

export const StatLine: React.FC<StatLineProps> = ({
  label,
  value,
  valueComponent, // Destructure new prop
  valueColor = "text-aegis-cyan",
  icon,
  valueGlow,
}) => {
  // Explicitly handle string conversion for null/undefined, default to '--'
  const getSafeString = (val: string | undefined | null): string => {
     if (typeof val === 'string') return val;
     if (val === null || val === undefined) return '--';
     // Use String() for potential non-string primitives
     return String(val);
  };

  const safeValue = getSafeString(value);
  const [displayValue, setDisplayValue] = useState(safeValue);
  const [isFlickering, setIsFlickering] = useState(false);
  const previousValueRef = useRef(safeValue);
  const flickerTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref to store timeout ID

  // Effect to handle value changes and trigger flicker
  useEffect(() => {
    const currentSafeValue = getSafeString(value);

    // Clear any existing flicker timeout if the value changes rapidly
    if (flickerTimeoutRef.current) {
      clearTimeout(flickerTimeoutRef.current);
      setIsFlickering(false); // Ensure flickering stops if interrupted
      flickerTimeoutRef.current = null;
    }

    // Only trigger flicker if the value actually changes from the last *stable* value
    // and it's not the initial default '--' changing to a real value.
    if (currentSafeValue !== previousValueRef.current && previousValueRef.current !== '--') {
      setIsFlickering(true);
      setDisplayValue(generateFlicker(currentSafeValue.length || 5)); // Show flicker text

      // Set timeout to revert to the actual value
      flickerTimeoutRef.current = setTimeout(() => {
        setIsFlickering(false);
        setDisplayValue(currentSafeValue);
        previousValueRef.current = currentSafeValue; // Update previous value *after* flicker completes
        flickerTimeoutRef.current = null; // Clear the ref
      }, 100); // Flicker duration

    } else if (displayValue !== currentSafeValue) {
      // If no flicker needed (e.g., initial load or value didn't change meaningfully), just update display
      // Also update previousValueRef here to prevent flicker on next identical update
      setDisplayValue(currentSafeValue);
      previousValueRef.current = currentSafeValue;
    }

    // Cleanup function to clear timeout on unmount or before next run
    return () => {
      if (flickerTimeoutRef.current) {
        clearTimeout(flickerTimeoutRef.current);
      }
    };
  // Only run flicker effect if we are displaying text, not a component
  }, [value, valueComponent]); // Add valueComponent dependency

  return (
    <View className="flex-row justify-between items-center mb-1">
      <View className="flex-row items-center">
        {icon ? <View className="mr-2">{icon}</View> : null}
        <Text className="text-aegis-cyan font-mono text-sm uppercase">{label}:</Text>
      </View>
      {valueComponent ? (
        <View>{valueComponent}</View>
      ) : (
        <Text className={`
          font-mono text-sm
          ${isFlickering ? 'text-aegis-orange text-shadow-glow-orange' : valueColor}
          ${!isFlickering && valueGlow === 'orange' ? 'text-shadow-glow-orange' : ''}
          ${!isFlickering && valueGlow === 'cyan' ? 'text-shadow-glow-cyan' : ''}
          ${!isFlickering && valueGlow === 'red' ? 'text-shadow-glow-red' : ''}
        `}>
          {displayValue}
        </Text>
      )}
    </View>
  );
};
