import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { LogLine } from '../ui/LogLine';

interface ScrollingLogSectionProps {
  title: string;
  logMessages: string[];
  maxLines?: number;
  scrollSpeed?: number;
  lineHeight?: number;
}

const DEFAULT_LINE_HEIGHT = 30;

export const ScrollingLogSection = ({
  title,
  logMessages,
  maxLines = 50,
  scrollSpeed = 50,
  lineHeight = DEFAULT_LINE_HEIGHT,
}: ScrollingLogSectionProps) => {
  const scrollAnim = useRef(new Animated.Value(0)).current;
  const [containerHeight, setContainerHeight] = useState(0);
  const [singleSetHeight, setSingleSetHeight] = useState(0);
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);

  // Keep only the most recent messages
  const displayBuffer = logMessages.slice(-maxLines);

  // Handle single content set measurement
  const handleSingleSetLayout = (event: any) => {
    const height = event.nativeEvent.layout.height;
    if (height > 0 && height !== singleSetHeight) {
      setSingleSetHeight(height);
    }
  };

  // Handle container layout changes
  const handleContainerLayout = (event: any) => {
    const height = event.nativeEvent.layout.height;
    if (height > 0 && height !== containerHeight) {
      setContainerHeight(height);
    }
  };

  // Start or restart the seamless animation
  useEffect(() => {
    // Clean up any existing animation
    if (animationRef.current) {
      animationRef.current.stop();
    }

    // Only start animation when we have valid measurements and enough content to scroll
    if (singleSetHeight <= 0 || displayBuffer.length === 0) {
      return;
    }

    // For seamless infinite scrolling:
    // 1. Start with content positioned at y=0
    // 2. Scroll exactly one set height distance (bringing second copy into view)
    // 3. When animation completes, immediately reset to y=0 (user won't notice as identical content is showing)
    // 4. Repeat

    const duration = (singleSetHeight / scrollSpeed) * 1000;
    
    // Reset to initial position
    scrollAnim.setValue(0);

    // Define the animation loop
    const createAndStartAnimation = () => {
      Animated.timing(scrollAnim, {
        toValue: -singleSetHeight,
        duration: duration,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(({ finished }) => {
        if (finished) {
          // Reset position without animation (visually seamless since duplicate content)
          scrollAnim.setValue(0);
          // Start the animation again
          createAndStartAnimation();
        }
      });
    };

    createAndStartAnimation();

    // Clean up on unmount
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, [singleSetHeight, scrollSpeed, displayBuffer.length, scrollAnim]);

  return (
    <View className="mb-4">
      <Text 
        className="text-aegis-orange font-title text-lg uppercase border-b border-aegis-orange/80 pb-1 mb-2" 
        style={{ textShadowColor: '#FF6600', textShadowRadius: 8 }}
      >
        {title}
      </Text>
      
      <View 
        style={{ height: lineHeight * 5, overflow: 'hidden' }}
        onLayout={handleContainerLayout}
      >
        <Animated.View style={{ transform: [{ translateY: scrollAnim }] }}>

          <View onLayout={handleSingleSetLayout}>
            {displayBuffer.map((msg, index) => (
              <LogLine 
                key={`set1-${index}`} 
                message={msg} 
                isError={/ERROR|CRITICAL|OFFLINE/i.test(msg)} 
              />
            ))}
          </View>
          

          <View>
            {displayBuffer.map((msg, index) => (
              <LogLine 
                key={`set2-${index}`} 
                message={msg} 
                isError={/ERROR|CRITICAL|OFFLINE/i.test(msg)} 
              />
            ))}
          </View>
        </Animated.View>
        

        <LinearGradient
          colors={['rgba(0,0,0,1)', 'rgba(0,0,0,0)']}
          style={styles.fadeOverlayTop}
          pointerEvents="none"
        />
        

        <LinearGradient
          colors={['rgba(0,0,0,0)', 'rgba(0,0,0,1)']}
          style={styles.fadeOverlayBottom}
          pointerEvents="none"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  fadeOverlayTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: DEFAULT_LINE_HEIGHT * 1.5,
  },
  fadeOverlayBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: DEFAULT_LINE_HEIGHT * 1.5,
  },
});