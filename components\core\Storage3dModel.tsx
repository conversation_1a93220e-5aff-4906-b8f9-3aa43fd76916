import React, {
  useRef,
  useMemo,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useFrame } from "@react-three/fiber/native";
import * as THREE from "three";
import { Box, Plane } from "@react-three/drei/native";

interface Storage3dModelProps {
  storageUsage?: number | null;
}

const ROWS = 16;
const COLS = 10;
const TOTAL_UNITS = ROWS * COLS;

const UNIT_WIDTH = 0.18;
const UNIT_HEIGHT = 0.08;
const UNIT_DEPTH = 0.05;
const GAP_X = 0.04;
const GAP_Y = 0.04;

const GRID_WIDTH = COLS * UNIT_WIDTH + (COLS - 1) * GAP_X;
const GRID_HEIGHT = ROWS * UNIT_HEIGHT + (ROWS - 1) * GAP_Y;

const USED_COLOR = Object.freeze(new THREE.Color("#FF7F00"));
const FREE_COLOR = Object.freeze(new THREE.Color("#00BFFF"));
const RED_COLOR = Object.freeze(new THREE.Color("#FF0000"));

const boxGeometry = new THREE.BoxGeometry(
  UNIT_WIDTH,
  UNIT_HEIGHT,
  UNIT_DEPTH + 0.1,
  2,
  2,
  2
);

const updateUsageMap = (
  prevMap: boolean[],
  targetUsedCount: number
): boolean[] => {
  const currentUsedCount = prevMap.filter(Boolean).length;

  if (currentUsedCount === targetUsedCount) return prevMap;

  const newMap = [...prevMap];

  if (currentUsedCount < targetUsedCount) {
    const unusedIndices = [];
    for (let i = 0; i < newMap.length; i++) {
      if (!newMap[i]) unusedIndices.push(i);
    }

    for (let i = unusedIndices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [unusedIndices[i], unusedIndices[j]] = [
        unusedIndices[j],
        unusedIndices[i],
      ];
    }

    const blocksToChange = targetUsedCount - currentUsedCount;
    for (let i = 0; i < blocksToChange && i < unusedIndices.length; i++) {
      newMap[unusedIndices[i]] = true;
    }
  } else if (currentUsedCount > targetUsedCount) {
    const usedIndices = [];
    for (let i = 0; i < newMap.length; i++) {
      if (newMap[i]) usedIndices.push(i);
    }

    for (let i = usedIndices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [usedIndices[i], usedIndices[j]] = [usedIndices[j], usedIndices[i]];
    }

    const blocksToChange = currentUsedCount - targetUsedCount;
    for (let i = 0; i < blocksToChange && i < usedIndices.length; i++) {
      newMap[usedIndices[i]] = false;
    }
  }

  return newMap;
};

export const Storage3dModel: React.FC<Storage3dModelProps> = ({
  storageUsage = null,
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const usageValue = storageUsage ?? 0;
  const isAlerting = usageValue >= 0.85;

  const unitsDataRef = useRef<{
    meshes: (THREE.Mesh | null)[];
    materials: (THREE.MeshStandardMaterial | null)[];
    isUsed: boolean[];
    pulsePhases: Float32Array;
    lastUpdateTime: number;
    dirtyIndices: Set<number>;
    frameCounter: number;
  }>({
    meshes: new Array(TOTAL_UNITS).fill(null),
    materials: new Array(TOTAL_UNITS).fill(null),
    isUsed: new Array(TOTAL_UNITS).fill(false),
    pulsePhases: new Float32Array(TOTAL_UNITS).map(
      () => Math.random() * Math.PI * 2
    ),
    lastUpdateTime: 0,
    dirtyIndices: new Set<number>(),
    frameCounter: 0,
  });

  const staticUnits = useMemo(() => {
    const unitData = [];
    for (let r = 0; r < ROWS; r++) {
      for (let c = 0; c < COLS; c++) {
        const x = (c - (COLS - 1) / 2) * (UNIT_WIDTH + GAP_X);
        const y = ((ROWS - 1) / 2 - r) * (UNIT_HEIGHT + GAP_Y);
        unitData.push({
          key: `unit-${r}-${c}`,
          position: [x, y, 0] as [number, number, number],
        });
      }
    }
    return unitData;
  }, []);

  const updateUsageCallback = useCallback((targetUsedCount: number) => {
    const currentMap = unitsDataRef.current.isUsed;
    const currentUsedCount = currentMap.filter(Boolean).length;

    if (currentUsedCount !== targetUsedCount) {
      const newMap = updateUsageMap(currentMap, targetUsedCount);

      for (let i = 0; i < TOTAL_UNITS; i++) {
        if (newMap[i] !== currentMap[i]) {
          unitsDataRef.current.dirtyIndices.add(i);
        }
      }

      unitsDataRef.current.isUsed = newMap;
    }
  }, []);

  useEffect(() => {
    const targetUsedCount = Math.floor(usageValue * TOTAL_UNITS);
    updateUsageCallback(targetUsedCount);
  }, [usageValue, updateUsageCallback]);

  useEffect(() => {
    const shuffleIntervalId = setInterval(() => {
      const targetUsedCount = Math.floor(usageValue * TOTAL_UNITS);

      const { isUsed } = unitsDataRef.current;

      const toggleCount = Math.floor(TOTAL_UNITS * 0.1);
      const shuffleIndices = new Array(TOTAL_UNITS);
      for (let i = 0; i < TOTAL_UNITS; i++) {
        shuffleIndices[i] = i;
      }

      for (let i = 0; i < toggleCount; i++) {
        const j = i + Math.floor(Math.random() * (TOTAL_UNITS - i));
        [shuffleIndices[i], shuffleIndices[j]] = [
          shuffleIndices[j],
          shuffleIndices[i],
        ];
      }

      const newMap = [...isUsed];
      let usedChange = 0;

      for (let i = 0; i < toggleCount; i++) {
        const idx = shuffleIndices[i];
        if (newMap[idx]) usedChange--;
        else usedChange++;
        newMap[idx] = !newMap[idx];
        unitsDataRef.current.dirtyIndices.add(idx);
      }

      if (usedChange > 0) {
        const usedIndices = [];
        for (let i = 0; i < newMap.length; i++) {
          if (newMap[i]) usedIndices.push(i);
        }

        for (let i = 0; i < usedChange && usedIndices.length > 0; i++) {
          const randomIndex = Math.floor(Math.random() * usedIndices.length);
          const idx = usedIndices.splice(randomIndex, 1)[0];
          newMap[idx] = false;
          unitsDataRef.current.dirtyIndices.add(idx);
        }
      } else if (usedChange < 0) {
        const unusedIndices = [];
        for (let i = 0; i < newMap.length; i++) {
          if (!newMap[i]) unusedIndices.push(i);
        }

        for (let i = 0; i < -usedChange && unusedIndices.length > 0; i++) {
          const randomIndex = Math.floor(Math.random() * unusedIndices.length);
          const idx = unusedIndices.splice(randomIndex, 1)[0];
          newMap[idx] = true;
          unitsDataRef.current.dirtyIndices.add(idx);
        }
      }

      unitsDataRef.current.isUsed = newMap;
      unitsDataRef.current.lastUpdateTime = Date.now();
    }, 3000);

    return () => clearInterval(shuffleIntervalId);
  }, [usageValue]);

  useFrame((state, delta) => {
    const time = state.clock.getElapsedTime();
    const data = unitsDataRef.current;
    const { meshes, materials, isUsed, pulsePhases, dirtyIndices } = data;

    data.frameCounter++;

    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(time * 0.1) * 0.05;
      groupRef.current.rotation.x = Math.cos(time * 0.08) * 0.05;
    }

    if (dirtyIndices.size > 0) {
      dirtyIndices.forEach((i) => {
        const material = materials[i];
        if (!material) return;

        const isCurrentlyUsed = isUsed[i];

        if (isCurrentlyUsed) {
          if (isAlerting) {
            material.color.copy(RED_COLOR);
            material.emissive.copy(RED_COLOR);
          } else {
            material.color.copy(USED_COLOR);
            material.emissive.copy(USED_COLOR);
          }
        } else {
          material.color.copy(FREE_COLOR);
          material.emissive.copy(FREE_COLOR);
          material.emissiveIntensity = 0.4;
        }
      });

      dirtyIndices.clear();
    }

    if (data.frameCounter % 3 === 0) {
      const frameModulo = Math.floor(time * 20) % 8;

      for (let i = frameModulo; i < materials.length; i += 8) {
        const material = materials[i];
        if (!material) continue;

        const isCurrentlyUsed = isUsed[i];

        if (isCurrentlyUsed) {
          if (isAlerting) {
            const pulseIntensity =
              0.6 + Math.sin(time * 5 + pulsePhases[i]) * 0.3;
            material.emissiveIntensity = pulseIntensity;
          } else {
            const shouldPulse = Math.sin(time * 0.5 + pulsePhases[i]) > 0.9;
            const pulseIntensity = shouldPulse ? 0.8 : 0.6;
            material.emissiveIntensity = pulseIntensity;
          }
        }
      }
    }
  });

  const standardMaterialProps = useMemo(
    () => ({
      color: FREE_COLOR.clone(),
      emissive: FREE_COLOR.clone(),
      emissiveIntensity: 0.4,
      roughness: 0.4,
      metalness: 0.1,
      wireframe: true,
    }),
    []
  );

  const backgroundPlane = useMemo(
    () => (
      <Plane
        args={[GRID_WIDTH + 0.5, GRID_HEIGHT + 0.5]}
        position={[0, 0, -UNIT_DEPTH * 1.5]}
      >
        <meshStandardMaterial
          color={FREE_COLOR.clone()}
          wireframe={true}
          transparent
          opacity={0.1}
          emissive={FREE_COLOR.clone()}
          emissiveIntensity={0.05}
          side={THREE.DoubleSide}
        />
      </Plane>
    ),
    []
  );

  const createMeshRef = useCallback((index: number) => {
    return (mesh: THREE.Mesh | null) => {
      if (mesh) {
        unitsDataRef.current.meshes[index] = mesh;
        unitsDataRef.current.materials[index] =
          mesh.material as THREE.MeshStandardMaterial;
        unitsDataRef.current.dirtyIndices.add(index);
      }
    };
  }, []);

  return (
    <group ref={groupRef} scale={1.9} position={[0, 0, -0.7]}>
      {backgroundPlane}

      {staticUnits.map((unit, index) => (
        <mesh
          key={unit.key}
          geometry={boxGeometry}
          position={unit.position}
          ref={createMeshRef(index)}
        >
          <meshStandardMaterial {...standardMaterialProps} />
        </mesh>
      ))}
    </group>
  );
};
