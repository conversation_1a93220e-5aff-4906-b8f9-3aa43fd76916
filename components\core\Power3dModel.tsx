import React, { useRef, useMemo, useState } from "react";
import { useFrame } from "@react-three/fiber/native";
import * as THREE from "three";
import {
  Icosahedron,
  Torus,
  Sphere,
  Points,
  PointMaterial,
  Cylinder,
  Octahedron,
  Line,
} from "@react-three/drei/native";

interface Power3dModelProps {
  batteryLevel: number | undefined; // 0 to 1
  batteryStatus: string; // e.g., 'CHARGING', 'DISCHARGING', 'FULL', 'UNKNOWN'
}

// Colors
const COLOR_CYAN = "#00BFFF";
const COLOR_ORANGE = "#FF7F00";
const COLOR_RED = "#FF0000";
const COLOR_YELLOW = "#FFFF00"; // Charging color
const COLOR_WHITE = "#FFFFFF"; // For charging beams / inner core charging
const COLOR_GRAY = "#AAAAAA";
const COLOR_GREEN_NERV = "#00FF00"; // NERV-style Green
const COLOR_SHELL_ALPHA = 0.15; // Opacity for ring shells

// Heavily Enhanced Thematic Power Model V7 (Refined Ring Colors/Speed)
// TODO: Refactor large useMemo and useFrame blocks for better readability/maintainability.
export const Power3dModel: React.FC<Power3dModelProps> = ({
  batteryLevel,
  batteryStatus,
}) => {
  const groupRef = useRef<THREE.Group>(null);
  const coreOuterRef = useRef<THREE.Mesh>(null);
  const coreInnerRef = useRef<THREE.Mesh>(null);
  const coreInnermostRef = useRef<THREE.Mesh>(null);
  const ringGroupYRef = useRef<THREE.Group>(null);
  const ringGroupXRef = useRef<THREE.Group>(null);
  const particlesRef = useRef<THREE.Points>(null);
  const containmentRef = useRef<THREE.Mesh>(null);
  const chargingBeamsRef = useRef<THREE.Group>(null);
  const arcsRef = useRef<THREE.Group>(null);
  const arcVisibilityTimers = useRef<number[]>([]);
  // Refs to store the last applied thickness to avoid unnecessary geometry creation
  const lastAppliedThicknessYRef = useRef<number[]>([]);
  const lastAppliedThicknessXRef = useRef<number[]>([]);

  const levelValue = batteryLevel ?? 0;
  const isCharging = batteryStatus === "CHARGING";
  const isDischarging = batteryStatus === "DISCHARGING";
  const isFull = batteryStatus === "FULL";
  const isLow = levelValue < 0.2 && !isCharging; // Critical low < 20%

  // --- Determine colors, intensities, and ring thickness ---
  const {
    outerCoreColor,
    innerCoreColor,
    innermostCoreColor,
    coreIntensity,
    ring1Color,
    ring2Color,
    ring3Color,
    ring4Color,
    ring5Color,
    ringIntensity,
    ringTubeThickness,
    particleColor,
    particleSize,
    particleOpacity,
    containmentOpacity,
    containmentColor,
  } = useMemo(() => {
    let oCoreC = COLOR_CYAN;
    let iCoreC = COLOR_GREEN_NERV;
    let iMostC = COLOR_GREEN_NERV;
    let r1 = COLOR_CYAN,
      r2 = COLOR_CYAN,
      r3 = COLOR_CYAN,
      r4 = COLOR_CYAN,
      r5 = COLOR_CYAN; // Ring colors
    let pColor = COLOR_CYAN;
    let contColor = COLOR_CYAN;

    // --- Apply Corrected Threshold Logic (Discharging) ---
    if (!isCharging && !isFull) {
      // Ring Colors (Cyan -> Orange -> Red) - Outer rings change first
      if (levelValue < 0.9) r5 = COLOR_ORANGE; // Ring 5 Orange < 90%
      if (levelValue < 0.8) r5 = COLOR_RED; // Ring 5 Red < 80%

      if (levelValue < 0.7) r4 = COLOR_ORANGE; // Ring 4 Orange < 70%
      if (levelValue < 0.6) r4 = COLOR_RED; // Ring 4 Red < 60%

      if (levelValue < 0.6) r3 = COLOR_ORANGE; // Ring 3 Orange < 60%
      if (levelValue < 0.4) r3 = COLOR_RED; // Ring 3 Red < 40%

      if (levelValue < 0.4) r2 = COLOR_ORANGE; // Ring 2 Orange < 40%
      if (levelValue < 0.2) r2 = COLOR_RED; // Ring 2 Red < 20%

      if (levelValue < 0.2) r1 = COLOR_ORANGE; // Ring 1 Orange < 20% (Stays Orange)

      // Core Colors
      if (levelValue < 0.4) oCoreC = COLOR_ORANGE;
      if (levelValue < 0.2) {
        // Critical Low Overrides
        oCoreC = COLOR_RED;
        iCoreC = COLOR_RED;
        iMostC = COLOR_RED;
        // Ring colors already set by above logic for < 0.2
      }

      // Particle color
      pColor =
        levelValue < 0.5 ? (isLow ? COLOR_RED : COLOR_ORANGE) : COLOR_CYAN;
    }

    // --- Base Intensities/Sizes/Opacities ---
    let cIntensity = 0.5 + levelValue * 0.7;
    let rIntensity = 0.4 + levelValue * 0.5;
    let pSize = 0.02 + levelValue * 0.03;
    let pOpacity = 0.5 + levelValue * 0.4;
    let contOpacity = 0.15 + levelValue * 0.2;
    let tubeThickness = 0.015 + levelValue * 0.025;

    // --- Overrides for Specific States ---
    if (isCharging) {
      oCoreC = iCoreC = r1 = r2 = r3 = r4 = r5 = contColor = COLOR_YELLOW;
      iMostC = COLOR_WHITE;
      cIntensity = 1.0 + Math.sin(Date.now() * 0.006) * 0.4;
      rIntensity = 0.7 + Math.sin(Date.now() * 0.006 + 1) * 0.2;
      pColor = COLOR_WHITE;
      pSize = 0.05 + Math.sin(Date.now() * 0.006 + 2) * 0.01;
      pOpacity = 0.9 + Math.sin(Date.now() * 0.006 + 3) * 0.1;
      contOpacity = 0.4 + Math.sin(Date.now() * 0.006 + 4) * 0.1;
      tubeThickness = 0.04 + Math.sin(Date.now() * 0.006 + 5) * 0.005;
    } else if (isLow) {
      // isLow means < 0.2 and not charging
      cIntensity = 0.8 + Math.sin(Date.now() * 0.012) * 0.3;
      rIntensity = 0.6 + Math.sin(Date.now() * 0.012 + 1) * 0.2;
      pColor = COLOR_RED;
      pSize = 0.035;
      pOpacity = 0.7;
      contOpacity = 0.1;
      contColor = COLOR_RED;
      tubeThickness = 0.015;
    } else if (isFull) {
      oCoreC =
        iCoreC =
        iMostC =
        r1 =
        r2 =
        r3 =
        r4 =
        r5 =
        contColor =
          COLOR_CYAN;
      cIntensity = 1.2;
      rIntensity = 0.8;
      pColor = COLOR_CYAN;
      pSize = 0.05;
      pOpacity = 0.9;
      contOpacity = 0.4;
      tubeThickness = 0.04;
    }

    return {
      outerCoreColor: oCoreC,
      innerCoreColor: iCoreC,
      innermostCoreColor: iMostC,
      coreIntensity: cIntensity,
      ring1Color: r1,
      ring2Color: r2,
      ring3Color: r3,
      ring4Color: r4,
      ring5Color: r5,
      ringIntensity: rIntensity,
      ringTubeThickness: tubeThickness,
      particleColor: pColor,
      particleSize: pSize,
      particleOpacity: pOpacity,
      containmentOpacity: contOpacity,
      containmentColor: contColor,
    };
  }, [levelValue, isCharging, isLow, isFull, isDischarging]);

  // --- Particle Generation ---
  const particleCount = 150 + Math.floor(levelValue * 200);
  const particlePositions = useMemo(() => {
    const positions = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = 0.7 + Math.random() * 1.4;
      const phi = Math.acos(-1 + 2 * Math.random());
      const theta = Math.sqrt(particleCount * Math.PI) * phi;
      positions[i3] = radius * Math.cos(theta) * Math.sin(phi);
      positions[i3 + 1] = radius * Math.sin(theta) * Math.sin(phi);
      positions[i3 + 2] = radius * Math.cos(phi);
    }
    return positions;
  }, [particleCount]);
  const initialRadii = useMemo(() => {
    const radii = new Float32Array(particleCount);
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      radii[i] = Math.sqrt(
        particlePositions[i3] ** 2 +
          particlePositions[i3 + 1] ** 2 +
          particlePositions[i3 + 2] ** 2
      );
    }
    return radii;
  }, [particlePositions]);

  // --- Animation ---
  useFrame((state, delta) => {
    const time = state.clock.getElapsedTime();

    // --- Calculate Speed Multipliers ---
    let speedMultiplier = 0.5 + levelValue * 1.5; // Base speed + level scaling for discharging
    if (isCharging) speedMultiplier = 2.0; // Faster when charging
    if (isLow) speedMultiplier = 0.3; // Slower when critically low
    // --- End Speed Multipliers ---

    const rotationSpeed = delta * speedMultiplier * 0.3; // Base rotation factor
    const particleFlowSpeed =
      delta * (isCharging ? 1.5 : isDischarging ? 0.8 : 0);
    const particleDirection = isCharging ? -1 : isDischarging ? 1 : 0;

    if (groupRef.current) groupRef.current.rotation.y += rotationSpeed * 0.2; // Slower overall group rotation

    // Core animation
    if (coreOuterRef.current?.material instanceof THREE.MeshStandardMaterial) {
      coreOuterRef.current.rotation.y += delta * 0.15 * speedMultiplier;
      coreOuterRef.current.rotation.x -= delta * 0.1 * speedMultiplier;
      coreOuterRef.current.material.color.set(outerCoreColor);
      coreOuterRef.current.material.emissive.set(outerCoreColor);
      coreOuterRef.current.material.emissiveIntensity = coreIntensity;
    }
    if (coreInnerRef.current?.material instanceof THREE.MeshStandardMaterial) {
      coreInnerRef.current.rotation.y -= delta * 0.2 * speedMultiplier;
      coreInnerRef.current.rotation.z += delta * 0.15 * speedMultiplier;
      coreInnerRef.current.material.color.set(innerCoreColor);
      coreInnerRef.current.material.emissive.set(innerCoreColor);
      coreInnerRef.current.material.emissiveIntensity = ringIntensity;
    }
    if (
      coreInnermostRef.current?.material instanceof THREE.MeshStandardMaterial
    ) {
      coreInnermostRef.current.rotation.y +=
        delta * 0.3 * speedMultiplier * 1.2;
      coreInnermostRef.current.rotation.x -=
        delta * 0.25 * speedMultiplier * 1.2;
      coreInnermostRef.current.material.color.set(innermostCoreColor);
      coreInnermostRef.current.material.emissive.set(innermostCoreColor);
      const pulseSpeed = isLow ? 12 : 5;
      coreInnermostRef.current.material.emissiveIntensity =
        0.5 + Math.sin(time * pulseSpeed) * 0.4;
    }

    // Rings animation - Apply specific colors AND update thickness AND speed
    const yRingColors = [ring1Color, ring3Color, ring5Color];
    if (ringGroupYRef.current) {
      ringGroupYRef.current.rotation.y += rotationSpeed * 1.8; // Faster base rotation for group
      ringGroupYRef.current.children.forEach((group, index) => {
        if (group instanceof THREE.Group) {
          const ring = group.children[0] as THREE.Mesh;
          const shell = group.children[1] as THREE.Mesh;
          if (
            ring instanceof THREE.Mesh &&
            ring.material instanceof THREE.MeshStandardMaterial &&
            shell instanceof THREE.Mesh &&
            shell.material instanceof THREE.MeshStandardMaterial
          ) {
            const color = yRingColors[index] || COLOR_CYAN;
            ring.material.color.set(color);
            ring.material.emissive.set(color);
            ring.material.emissiveIntensity = ringIntensity * (1 + index * 0.1);
            group.rotation.z +=
              delta * (index % 2 === 0 ? 0.3 : -0.2) * speedMultiplier * 1.5; // Rotate the group faster
            const currentArgs = (ring.geometry as THREE.TorusGeometry)
              .parameters;
            const shellArgs = (shell.geometry as THREE.TorusGeometry)
              .parameters;
            const shellThickness = ringTubeThickness + 0.01;

            // Only update geometry if the calculated thickness has actually changed significantly
            if (
              Math.abs(
                lastAppliedThicknessYRef.current[index] - ringTubeThickness
              ) > 0.001
            ) {
              if (__DEV__) {
                console.log(
                  `[Power3D] Updating Y Ring ${index} Geometry - Thickness: ${ringTubeThickness.toFixed(
                    3
                  )}`
                );
              }
              ring.geometry.dispose(); // Dispose old geometry
              ring.geometry = new THREE.TorusGeometry(
                currentArgs.radius,
                ringTubeThickness,
                currentArgs.radialSegments,
                currentArgs.tubularSegments
              );
              shell.geometry.dispose(); // Dispose old geometry
              shell.geometry = new THREE.TorusGeometry(
                shellArgs.radius,
                shellThickness,
                shellArgs.radialSegments,
                shellArgs.tubularSegments
              );
              lastAppliedThicknessYRef.current[index] = ringTubeThickness; // Store the newly applied thickness
            }

            // Update materials regardless of geometry change
            shell.material.color.set(color);
            shell.material.emissive.set(color);
            shell.material.emissiveIntensity = ringIntensity * 0.1;
          }
        }
      });
    }
    const xRingColors = [ring2Color, ring4Color];
    if (ringGroupXRef.current) {
      ringGroupXRef.current.rotation.x += rotationSpeed * 1.6; // Faster base rotation
      ringGroupXRef.current.children.forEach((group, index) => {
        if (group instanceof THREE.Group) {
          const ring = group.children[0] as THREE.Mesh;
          const shell = group.children[1] as THREE.Mesh;
          if (
            ring instanceof THREE.Mesh &&
            ring.material instanceof THREE.MeshStandardMaterial &&
            shell instanceof THREE.Mesh &&
            shell.material instanceof THREE.MeshStandardMaterial
          ) {
            const color = xRingColors[index] || COLOR_CYAN;
            ring.material.color.set(color);
            ring.material.emissive.set(color);
            ring.material.emissiveIntensity = ringIntensity * (1 + index * 0.1);
            group.rotation.z +=
              delta * (index % 2 === 0 ? -0.25 : 0.18) * speedMultiplier * 1.5; // Rotate the group faster
            const currentArgs = (ring.geometry as THREE.TorusGeometry)
              .parameters;
            const shellArgs = (shell.geometry as THREE.TorusGeometry)
              .parameters;
            const shellThickness = ringTubeThickness + 0.01;

            // Only update geometry if the calculated thickness has actually changed significantly
            if (
              Math.abs(
                lastAppliedThicknessXRef.current[index] - ringTubeThickness
              ) > 0.001
            ) {
              if (__DEV__) {
                console.log(
                  `[Power3D] Updating X Ring ${index} Geometry - Thickness: ${ringTubeThickness.toFixed(
                    3
                  )}`
                );
              }
              ring.geometry.dispose(); // Dispose old geometry
              ring.geometry = new THREE.TorusGeometry(
                currentArgs.radius,
                ringTubeThickness,
                currentArgs.radialSegments,
                currentArgs.tubularSegments
              );
              shell.geometry.dispose(); // Dispose old geometry
              shell.geometry = new THREE.TorusGeometry(
                shellArgs.radius,
                shellThickness,
                shellArgs.radialSegments,
                shellArgs.tubularSegments
              );
              lastAppliedThicknessXRef.current[index] = ringTubeThickness; // Store the newly applied thickness
            }

            // Update materials regardless of geometry change
            shell.material.color.set(color);
            shell.material.emissive.set(color);
            shell.material.emissiveIntensity = ringIntensity * 0.1;
          }
        }
      });
    }

    // Containment field animation
    if (
      containmentRef.current?.material instanceof THREE.MeshStandardMaterial
    ) {
      const pulseFactor = isLow ? 0.6 + Math.sin(time * 12) * 0.4 : 1;
      containmentRef.current.rotation.y -= delta * 0.04;
      containmentRef.current.material.opacity =
        containmentOpacity * pulseFactor;
      containmentRef.current.material.color.set(containmentColor);
      containmentRef.current.material.emissive.set(containmentColor);
      containmentRef.current.material.emissiveIntensity =
        containmentOpacity * 0.6 * pulseFactor;
    }

    // Particles animation
    if (particlesRef.current) {
      const positions = particlesRef.current.geometry.attributes.position
        .array as Float32Array;
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        const initialR = initialRadii[i];
        const currentR = Math.sqrt(
          positions[i3] ** 2 + positions[i3 + 1] ** 2 + positions[i3 + 2] ** 2
        );
        let newR =
          currentR +
          particleDirection * particleFlowSpeed * (0.5 + Math.random() * 0.5);
        const minR = 0.7;
        const maxR = 2.1;
        if (newR < minR) newR = maxR - (minR - newR);
        if (newR > maxR) newR = minR + (newR - maxR);
        const scale = newR / currentR;
        if (currentR > 1e-5) {
          positions[i3] *= scale;
          positions[i3 + 1] *= scale;
          positions[i3 + 2] *= scale;
        }
      }
      particlesRef.current.geometry.attributes.position.needsUpdate = true;
      if (particlesRef.current.material instanceof THREE.PointsMaterial) {
        particlesRef.current.material.color.set(particleColor);
        particlesRef.current.material.size = particleSize;
        particlesRef.current.material.opacity = particleOpacity;
      }
    }

    // Charging Beams Animation
    if (chargingBeamsRef.current) {
      chargingBeamsRef.current.visible = isCharging;
      if (isCharging) {
        chargingBeamsRef.current.rotation.y += delta * 0.5;
        chargingBeamsRef.current.children.forEach((beam) => {
          if (
            beam instanceof THREE.Mesh &&
            beam.material instanceof THREE.MeshBasicMaterial
          ) {
            beam.material.opacity = 0.4 + Math.sin(time * 5) * 0.3;
          }
        });
      }
    }

    // Energy Arc Animation
    if (arcsRef.current) {
      const arcChance = isCharging ? 0.02 : isLow ? 0.03 : 0.005;
      arcsRef.current.children.forEach((arc, index) => {
        if (arc instanceof THREE.Line) {
          const timer = arcVisibilityTimers.current[index] || 0;
          if (timer <= 0) {
            if (Math.random() < arcChance) {
              arc.visible = true;
              arcVisibilityTimers.current[index] = 0.1 + Math.random() * 0.2;
              if (arc.material instanceof THREE.LineBasicMaterial) {
                arc.material.color.set(
                  isCharging ? COLOR_YELLOW : isLow ? COLOR_RED : COLOR_WHITE
                );
                arc.material.opacity = 0.8;
              }
            } else {
              arc.visible = false;
            }
          } else {
            arcVisibilityTimers.current[index] -= delta;
            if (arc.material instanceof THREE.LineBasicMaterial) {
              arc.material.opacity = Math.max(0, (timer / 0.3) * 0.8);
            }
            if (timer <= 0) arc.visible = false;
          }
        }
      });
    }
  });

  // --- Define Ring Radii ---
  const ringRadiiY = [0.7, 1.3, 1.9]; // Innermost, Middle, Outermost Y
  const ringRadiiX = [1.0, 1.6]; // Inner, Outer X

  return (
    <group ref={groupRef} scale={1.0} rotation={[0.1, 0, 0]}>
      <Icosahedron ref={coreOuterRef} args={[0.6, 1]} position={[0, 0, 0]}>
        <meshStandardMaterial flatShading={false} wireframe={true} />
      </Icosahedron>
      <Icosahedron ref={coreInnerRef} args={[0.3, 1]} position={[0, 0, 0]}>
        <meshStandardMaterial flatShading={false} wireframe={true} />
      </Icosahedron>
      <Octahedron ref={coreInnermostRef} args={[0.15, 0]} position={[0, 0, 0]}>
        <meshStandardMaterial flatShading={false} wireframe={false} />
      </Octahedron>

      <Sphere ref={containmentRef} args={[2.4, 32, 16]}>
        <meshStandardMaterial wireframe={true} transparent={true} />
      </Sphere>

      <group ref={ringGroupYRef}>
        {ringRadiiY.map((radius, i) => (
          <group
            key={`ringYGroup-${i}`}
            rotation={[Math.PI / 2, Math.PI * (i / 6), 0]}
          >
            <Torus args={[radius, ringTubeThickness, 16, 48]}>
              <meshStandardMaterial wireframe={true} />
            </Torus>
            <Torus args={[radius, ringTubeThickness + 0.01, 16, 48]}>
              <meshStandardMaterial
                wireframe={false}
                transparent={true}
                opacity={COLOR_SHELL_ALPHA}
                side={THREE.BackSide}
              />
            </Torus>
          </group>
        ))}
      </group>

      <group ref={ringGroupXRef}>
        {ringRadiiX.map((radius, i) => (
          <group
            key={`ringXGroup-${i}`}
            rotation={[0, Math.PI / 2, Math.PI * (i / 4)]}
          >
            <Torus args={[radius, ringTubeThickness, 16, 48]}>
              <meshStandardMaterial wireframe={true} />
            </Torus>
            <Torus args={[radius, ringTubeThickness + 0.01, 16, 48]}>
              <meshStandardMaterial
                wireframe={false}
                transparent={true}
                opacity={COLOR_SHELL_ALPHA}
                side={THREE.BackSide}
              />
            </Torus>
          </group>
        ))}
      </group>

      <Points
        key={particleCount}
        ref={particlesRef}
        positions={particlePositions}
        stride={3}
        frustumCulled={false}
      >
        <PointMaterial transparent sizeAttenuation={true} depthWrite={false} />
      </Points>

      <group ref={chargingBeamsRef} visible={false}>
        {[0, 1, 2, 3].map((i) => (
          <Cylinder
            key={`beam-${i}`}
            args={[0.02, 0.02, 3.5, 6]}
            position={[0, 0, 0]}
            rotation={[Math.PI / 2, 0, (Math.PI / 2) * i + Math.PI / 4]}
          >
            <meshBasicMaterial
              color={COLOR_WHITE}
              transparent
              opacity={0.6}
              blending={THREE.AdditiveBlending}
              side={THREE.DoubleSide}
            />
          </Cylinder>
        ))}
      </group>

      <group ref={arcsRef}>
        <Line
          points={[
            [0.7, 0, 0],
            [1.3, 0.1, 0.1],
          ]}
          color={COLOR_WHITE}
          lineWidth={1.5}
          visible={false}
          transparent
          opacity={0}
        />
        <Line
          points={[
            [0, 1.3, 0.2],
            [0, 1.9 * 0.9, 0.3],
          ]}
          color={COLOR_WHITE}
          lineWidth={1.5}
          visible={false}
          transparent
          opacity={0}
        />
        <Line
          points={[
            [1.0, 0.1, 0],
            [1.6 * 0.8, 0.2, 0.1],
          ]}
          color={COLOR_WHITE}
          lineWidth={1.5}
          visible={false}
          transparent
          opacity={0}
        />
        <Line
          points={[
            [0, -1.6, 0.1],
            [0, -2.4 * 0.8, 0.2],
          ]}
          color={COLOR_WHITE}
          lineWidth={1.5}
          visible={false}
          transparent
          opacity={0}
        />
      </group>
    </group>
  );
};
