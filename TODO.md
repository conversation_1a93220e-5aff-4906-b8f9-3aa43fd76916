# Aegis System Monitor - Project Status & TODO

## Project Summary

**Concept:** Aegis System Monitor is a mobile application (initially Android, potentially iOS) designed as a system monitoring utility. It aims to provide users with detailed insights into their device's performance and status through key statistics.

**Goal:** To display essential device information including:
    *   **CPU:** Static information (Architecture, Model Name - *Real-time usage is a future goal*).
    *   **Power:** Real-time Battery Level, Status (Charging/Discharging/Full), Low Power Mode status, Screen Brightness, Estimated Shutdown Time, historical battery level chart.
    *   **Storage:** Used and Total disk space, potentially simulated breakdown.
    *   **Network:** Connection Type (WiFi/Cellular/Ethernet), Status (Connected/Reachable), Signal Strength (WiFi/Cellular), Carrier, SSID, IP Addresses (Internal, External, Gateway), on-demand Speed Test (Download/Upload), Latency Test, historical test results chart.
    *   *(Removed)* **Memory/System Status:** ~~Total RAM, basic OS/Device info (*Real-time usage is currently infeasible for MVP*). App's own memory usage, potentially Page Size and Low Memory warnings.~~

**Unique Selling Point:** The app differentiates itself through a distinct visual identity heavily inspired by the high-tech, retro-futuristic, data-dense user interfaces seen in the anime *Neon Genesis Evangelion* (specifically the NERV command center monitors and MAGI system displays). The goal is to create a visually striking and thematic utility that is both functional and aesthetically engaging **without infringing on any copyrights**. All visual elements are original creations inspired by the *style* and *feel* of the source material, not direct copies.

**Structure:** Features a main dashboard (`app/index.tsx`) providing a summary overview with an interactive 3D core model, and separate detail pages for Power, Storage, and Network (`app/*.tsx`), each featuring its own unique, detailed, data-reactive 3D visualization and relevant stats.

**Core Technologies:**
*   **Framework:** React Native (using the Expo framework/toolchain with a Development Build setup to accommodate native dependencies).
*   **Language:** TypeScript (for type safety and maintainability).
*   **Styling:** NativeWind v4 (Tailwind CSS for React Native) with a custom theme (`tailwind.config.js` - Aegis Orange, Cyan, Red; fonts: SpaceMono, Orbitron). Includes text glows via `tailwindcss-textshadow`.
*   **State Management:** Zustand (for global state management, holding system data, log messages, UI state).
*   **Navigation:** Expo Router (v3, file-based routing).
*   **3D Graphics:** `@react-three/fiber` (R3F) and `@react-three/drei` for rendering complex, interactive 3D models within React Native, using `expo-gl`.
*   **2D Graphics:** `react-native-svg` (for custom icons and charts like Battery/Speed History).
*   **Animation:** React Native `Animated` API and R3F `useFrame` hook (for UI effects like flicker/flash and 3D model animations).
*   **Persistence:** `@react-native-async-storage/async-storage` (for saving battery and speed test history).
*   **System Info Modules:**
    *   `expo-device`: Static device info (model, manufacturer, OS, total memory, etc.).
    *   `expo-battery`: Real-time battery level, status, low power mode (via polling and listeners).
    *   `expo-file-system`: Total and free disk storage capacity, file operations for speed test.
    *   `@react-native-community/netinfo`: Network type (WiFi/Cellular/etc.) and connectivity status.
    *   `expo-brightness`: Screen brightness level.
    *   *(Planned)* `react-native-device-info`: For additional details like App Memory, potentially Page Size, OS Build ID, Kernel Version etc.
    *   *(Planned)* `expo-display`: For screen refresh rate.
*   **Haptics:** `expo-haptics` (for subtle feedback on interactions).

## Completed Tasks (Summary)

*   **Foundation:** Project setup, core dependencies, theme, basic layout.
*   **UI Enhancements:** Abstract background, scanlines, text glows, glitch effect, custom icons.
*   **Data Integration:** Fetching and displaying Battery, Storage, Network (basic), Total RAM, Static Device Info.
*   **Logging:** Implemented scrolling log sections for General, Power, Storage, and Network events.
*   **Charting:** Battery history logging (AsyncStorage) and SVG chart display with timeframe selection. Speed test history chart with toggles and dynamic axes.
*   **Detail Pages:** Created separate screens for Power, Storage, and Network. *(Removed CPU/System and Memory pages)*
*   **3D Models:**
    *   Implemented interactive `CoreModel` on the main dashboard.
    *   Created detailed, data-reactive 3D models for Power and Storage. *(Removed CPU/System and Memory models)*
    *   Redesigned `Network3dModel` ("Network Resonance Waves" concept with layered tubes, particles, latency jitter, grid).
*   **Network Screen Enhancements:** Added on-demand Speed/Latency tests, Public/Gateway IP display, Quality Indicator, Signal Visualizer, History Chart with toggles and improved labels/scaling.
*   **Other Features:** Session Uptime, Estimated Shutdown Time calculation.
*   *(Removed)* **Memory Screen Enhancements:** ~~Added App Memory usage display (requires build), Low Memory warning indicator, simulated activity bar, and enhanced `Memory3dModel` visuals.~~
*   **Code Review & Polish (April 2025):** Conducted comprehensive review of core components, state, utils, and config. Applied minor fixes, added TODOs, removed unused code/components (`Memory/System/Cpu` models, `Cpu/Ram` icons, `ProgressBar`, `generateFlicker` util, `AbstractBackground`).
*   **Performance Optimization (April 2025):** Addressed critical geometry regeneration issues in `Power3dModel` and `Network3dModel`. Optimized `Storage3dModel` state updates and material changes. Reduced element counts in `AbstractBackground` (before removal) and `GlitchOverlay`.
*   **UI Enhancements (April 2025):** Implemented repeating typewriter effect in `AppFooter`. Added `GlitchOverlay` to detail pages.


## Next Steps / Immediate TODO

*   **Pre-Deployment Monetization (Ads - Google Mobile Ads):**
    *   [X] **Setup AdMob Account:** User confirmed setup.
    *   [X] **Register App:** User confirmed registration.
    *   [X] **Obtain AdMob IDs:**
        *   Android App ID: `ca-app-pub-****************~**********`
        *   Banner Ad Unit ID (Android): `ca-app-pub-****************/**********`
        *   Interstitial Ad Unit ID (Android): (To be created/provided later)
        *   Rewarded Ad Unit ID (Android): (To be created/provided later)
    *   [X] **Install Library:** Removed deprecated `expo-ads-admob`. Installed `react-native-google-mobile-ads`.
    *   [X] **Configure `app.json`:** Removed `expo-ads-admob` plugin. Added `react-native-google-mobile-ads.android_app_id`.
    *   [X] **Update Ad Implementation Code:**
        *   [X] Located `AdMobBanner` in `app/_layout.tsx`.
        *   [X] Replaced with `BannerAd` component from `react-native-google-mobile-ads`.
        *   [X] Initialized the SDK in `app/_layout.tsx`.
        *   [X] Using Test ID (`TestIds.BANNER`) for Banner.
    *   [X] **Prepare Native Project:** Run `npx expo prebuild --platform android --clean` to include native dependencies. (Completed by user)
    *   [X] **Build Development Client (USER ACTION REQUIRED):** Build and install a custom Android development client (`.apk`) using your build process (e.g., `eas build --profile development --platform android`). **This is required to run the app with the new AdMob native code.** (Completed by user)
    *   [X] **Testing (Phase 1 - Test IDs):** After building and installing the development client, run the app and test banner ad implementation using AdMob's test ID. Verify placement and loading. (Completed by user, test banner verified)
    *   [X] **Implement Ad Components (Phase 2 - Interstitial/Rewarded - Future):**
        *   [X] Implement logic for Interstitial ads using `InterstitialAd` from `react-native-google-mobile-ads`. Use Test ID initially: `ca-app-pub-3940256098642544/1033173712`. (Initial logic implemented in `app/_layout.tsx` to show on back navigation from detail screens)
        *   [ ] (Optional) Implement Rewarded ads.
    *   [ ] **Testing (Phase 2 - Real IDs on Registered Device):** Replace test IDs with real IDs (`ca-app-pub-****************/**********` for banner, and interstitial ID when available). Test *only* on a registered test device. Ensure compliance with AdMob policies.
    *   [ ] **Update Privacy Policy:**
        *   [X] Draft policy created (`privacy.md`) reflecting AdMob usage.
        *   [ ] **ACTION NEEDED:** User to replace placeholder email, review policy, move to public repo (e.g., portfolio), enable GitHub Pages, and obtain the public URL.

1.  **Deployment Preparation (Android - Google Play Store):**
    *   [X] **Review/Finalize `app.json`:**
        *   [X] Verify `name`, `slug`, `owner` (owner assumed N/A for now).
        *   [X] Increment `version` (verified 1.0.0) and `android.versionCode` (added 1).
        *   [X] Confirm `orientation` (`portrait`).
        *   [ ] Ensure final `icon` and `splash` images are correctly configured and generated (paths verified, images need replacement).
        *   [X] Double-check `android.package` (set to `com.aegis.systemmonitor`).
        *   [ ] Verify `android.adaptiveIcon` (paths verified, images need replacement).
        *   [X] Review `android.permissions`; remove unnecessary ones (added `INTERNET`).
        *   [X] Check `plugins` configurations (e.g., `expo-build-properties`) (reviewed, seems standard).
    *   [X] **Generate Release Keystore:**
        *   [X] Create a signing key using `keytool` (Generated `android/app/aegis-release-key.keystore`).
        *   [X] Store the keystore file securely (outside version control) (`android/app/aegis-release-key.keystore` confirmed present in `.gitignore`).
        *   [X] Back up the keystore and its credentials (alias, passwords) (**CRITICAL ACTION NEEDED: User must securely back up the `.keystore` file and the passwords used during generation! Loss of this key means you cannot update the app.**) (User confirmed backup completed).
    *   [X] **Configure Build Signing (Using EAS Build):**
        *   [X] Set `credentialsSource` to `local` in `eas.json` under `build.production.android` to use the generated keystore.
    *   [ ] **Build Release Artifact:**
        *   [ ] Generate Android App Bundle (`.aab`) using `eas build -p android --profile production` (recommended) or a manual Gradle build (`cd android && ./gradlew bundleRelease`).
    *   [ ] **Thorough Testing:**
        *   [ ] Install and test the release build (`.aab` via internal sharing or `.apk` if generated) on multiple physical Android devices (different OS versions, screen sizes, manufacturers).
        *   [ ] Verify all core features, UI elements, navigation, and data persistence.
        *   [ ] Test performance, battery usage, and stability.
        *   [ ] Check permissions requests and handling.
        *   [ ] Test offline behavior if applicable.
    *   [ ] **Google Play Console Setup:**
        *   [ ] Ensure Google Play Console developer account is active.
        *   [ ] Prepare app title, short description, and full description.
        *   [ ] Create high-resolution app icons, screenshots (for phone, tablet), and feature graphic.
        *   [ ] Write/Host comprehensive Privacy Policy (**ACTION NEEDED:** Use the URL obtained from hosting the `privacy.md` draft via GitHub Pages or alternative).
        *   [ ] Complete the Content Rating questionnaire.
        *   [ ] Configure target audience, categories, and contact details.
        *   [ ] Set pricing (free/paid) and distribution countries.
    *   [ ] **Upload and Rollout:**
        *   [ ] Upload the `.aab` file to a new release in the Play Console.
        *   [ ] Configure release notes.
        *   [ ] Start rollout to Internal Testing, Closed Testing (Alpha), Open Testing (Beta), or Production track.
        *   [ ] Consider a staged rollout for Production releases.
        *   [ ] Monitor rollout for crashes and user feedback.

2.  **Dependency Audit:**
    *   [X] **List Dependencies:** Identify all packages in `dependencies` and `devDependencies` in `package.json` (Implicitly done during audit).
    *   [X] **Check Licenses:**
        *   [X] Use a tool like `npx license-checker --production --summary` or `yarn licenses list` to review licenses (Checked, primarily permissive licenses like MIT, ISC, BSD, Apache-2.0 found. Also found `Unlicense` (e.g., `stream-buffers`), considered acceptable. No major issues detected).
        *   [X] Ensure all production dependency licenses are compatible with app distribution (MIT, Apache 2.0, BSD, Unlicense are generally safe; check others like GPL carefully) (Confirmed).
    *   [X] **Check Vulnerabilities:**
        *   [X] Run `npm audit` or `yarn audit` to find known security vulnerabilities (Found 9 moderate in `postcss` <=8.4.30 via `tailwindcss-textshadow`).
        *   [X] Address critical/high severity vulnerabilities by updating packages or finding alternatives (Resolved by removing unused `tailwindcss-textshadow` dev dependency).
    *   [ ] **Check Maintenance Status:**
        *   [ ] Briefly review key dependencies' repositories (e.g., last commit date, open issues/PRs) to ensure they are actively maintained.
    *   [ ] **Review Bundle Size Impact (Optional but Recommended):**
        *   [ ] Use `npx expo export --dump-assetmap` or `react-native-bundle-visualizer` to analyze the size contribution of dependencies. Identify unexpectedly large packages.
    *   [ ] **Review Native Modules:**
        *   [ ] Pay extra attention to native dependencies (`@react-native-community/*`, `expo-*` modules with native code, other third-party native modules). Verify stability and compatibility.
    *   [X] **Remove Unused Dependencies:**
        *   [X] Use `npx depcheck` to identify packages imported in the code but not listed in `package.json`, or vice-versa (Ran `depcheck`, found several potential candidates, many likely false positives).
        *   [X] Uninstall confirmed unused packages (`npm uninstall` or `yarn remove`) (Reviewed `depcheck` results. Decided to keep potential candidates like `react-native-device-info` and Jest packages for future use).


## Future Goals (Post-MVP / Phase 4+)

*   **Real-time CPU Usage:** Investigate reliable methods (likely requires native modules).
*   **GPU Information:** Research feasibility of displaying GPU model/usage.
*   **Detailed Device Info:** Consider adding more static device/OS info if deemed necessary (may require native modules).
*   **Widgets:** Implement home screen widgets (iOS/Android).
*   **Performance Optimization:** Detailed profiling and optimization, especially for 3D rendering and background tasks.
*   **Settings Screen:** Allow user configuration (e.g., refresh rates, theme tweaks, chart options).
*   **In-App Purchase (Remove Ads):**
    *   [ ] **Plan:** Implement a non-consumable in-app purchase (IAP) option to permanently remove all ads (banner and interstitial).
    *   [ ] **Library:** Investigate and choose an IAP library (e.g., `expo-in-app-purchases` or `react-native-iap`).
    *   [ ] **UI:** Add a "Remove Ads" button/option, likely within a future Settings screen.
    *   [ ] **State Management:** Store the purchase status persistently (e.g., AsyncStorage or secure storage) and update the Zustand store.
    *   [ ] **Conditional Rendering:** Modify `app/_layout.tsx` (and potentially other places where ads might be added) to conditionally render ads based on the purchase status.
    *   [ ] **Platform Setup:** Configure the IAP product in Google Play Console (and App Store Connect if iOS is pursued).
    *   [ ] **Restore Purchases:** Implement functionality to restore purchases.
*   **Cross-Platform Polish:** Thorough testing and adjustments for iOS if pursued.
*   **Mini-Game: Intrusion Detection Radar (Daily Challenge):**
    *   [X] **Concept:** Daily playable mini-game (`app/radar.tsx`) where player defends against incoming "intrusions" on a stylized radar display by tapping them. Limited to one play per 24 hours. (Screen created, navigation added)
    *   [ ] **Gameplay:**
        *   [X] Create `RadarDisplay.tsx` component (Basic SVG structure, animated sweep, grid, markers implemented).
        *   [X] Create `IntrusionBlip.tsx` component (Basic visual implemented).
        *   [X] Implement basic blip spawning logic (random edge, interval-based).
        *   [X] Implement basic blip movement logic (towards center).
        *   [X] Implement tap-to-neutralize interaction (removes blip on press, basic score increment, haptics).
        *   [X] Define win/loss conditions (Basic loss: blip reaches center -> triggers `gameOver` state; Basic win: survive `gameDurationMs`).
        *   [X] Implement basic scoring system (state, increment on tap, display).
        *   [X] Implement basic Game Over/Win overlays (`RadarDisplay`).
        *   [ ] Implement scaling difficulty based on daily streak (`lib/gameLogic.ts`).
    *   [X] **Code Structure:** Refactored state and logic into `hooks/useGameLogic.ts`.
    *   [X] **State Management:**
        *   [X] Create `store/gameStore.ts` (Zustand) with state (`lastPlayedTimestamp`, `currentStreak`, `earnedMedals`) and basic actions.
        *   [X] Integrate `gameStore` with `AsyncStorage` for persistence.
        *   [X] Integrate `gameStore` actions (`incrementStreak`, `resetStreak`, `setLastPlayed`) into `useGameLogic`.
    *   [ ] **Availability & Notifications:**
        *   [X] Track `lastPlayedTimestamp` in `gameStore` / `AsyncStorage` (Done via store setup).
        *   [X] Implement availability check in `AppHeader` (disables button, changes color).
        *   [X] Implement availability check in `app/radar.tsx` (shows message if unavailable).
        *   [X] Add `expo-notifications` dependency.
        *   [X] Implement notification permission request (`app/_layout.tsx`).
        *   [X] Implement scheduling/canceling of "game available" local notification (`hooks/useGameLogic.ts`).
        *   [ ] Add flashing/highlighting state to radar navigation button when available.
        *   [ ] Add thematic alert messages to Header/Footer/Log when available.
    *   [ ] **Progression & Rewards:**
        *   [ ] Track `currentStreak` in `gameStore` / `AsyncStorage`.
        *   [ ] Design/Create 3D medal assets (`assets/models/medal_*.glb`).
        *   [ ] Track `earnedMedals` in `gameStore` / `AsyncStorage`.
        *   [ ] Create `MedalDisplay.tsx` component (using R3F).
        *   [ ] Create `StreakCounter.tsx` component.
    *   [ ] **Monetization (Rewarded Ads):**
        *   [ ] Implement "Streak Saver" option (watch ad to restore streak if missed day or lost game). Requires `react-native-google-mobile-ads` Rewarded Ad unit ID and implementation.
        *   [ ] (Optional) Consider Rewarded Ads for minor power-ups.
    *   [X] **File Structure:** Implemented initial structure (`app/radar.tsx`, `components/game/*`, `hooks/useGameLogic.ts`, `store/gameStore.ts`).
*   **Cross-Platform Polish:** Thorough testing and adjustments for iOS if pursued.
*   **Advanced 3D Model Enhancements:** Shaders (Fresnel, noise), texture mapping (hex grids), post-processing (Bloom).
