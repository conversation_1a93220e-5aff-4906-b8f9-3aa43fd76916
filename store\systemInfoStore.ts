import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Battery from 'expo-battery';
import * as FileSystem from 'expo-file-system';
import * as Device from 'expo-device';
import { Dimensions } from 'react-native'; // Import Dimensions
import { NetInfoState } from '@react-native-community/netinfo';
// Removed unused DeviceInfo import

// --- Types ---
interface BatteryHistoryEntry {
  timestamp: number;
  level: number;
}

export type BatteryChartTimeframe = '1h' | '6h' | '24h';

export interface SystemInfoState { // Exporting the interface
  // Battery
  batteryLevel: number | undefined;
  batteryStatus: string;
  lowPowerModeEnabled: boolean | null;
  batteryHistory: BatteryHistoryEntry[];
  batteryChartTimeframe: BatteryChartTimeframe;
  estimatedShutdownTime: number | null;
  // screenBrightness: number | null; // Removed unused state
  // Storage
  totalStorage: number | undefined;
  freeStorage: number | undefined;
  // Memory
  totalMemory: number | null;
  // Network
  netInfo: NetInfoState | null;
  // CPU (Placeholders)
  cpuUsage: number | undefined;
  cpuSpeed: string;
  // Device Info (Static)
  manufacturer: string | null;
  modelName: string | null;
  cpuArchitectures: string[] | null;
  osName: string | null;
  osVersion: string | null;
  deviceYearClass: number | null;
  // Display Info
  screenWidth: number | null;
  screenHeight: number | null;
  screenScale: number | null;
  // Public IP
  publicIpAddress: string | null;
  // Latency Test
  latencyMs: number | null;
  isTestingLatency: boolean;
  // Speed Test History
  speedTestHistory: { timestamp: number; latency: number | null }[];
  // Log Messages
  logMessages: string[];
  storageLogMessages: string[];
  powerLogMessages: string[];
  networkLogMessages: string[]; // Added network log
  // Actions
  fetchInitialData: () => Promise<void>;
  logBatteryHistory: () => Promise<void>;
  addLogMessage: (message: string) => void;
  addStorageLogMessage: (message: string) => void;
  addPowerLogMessage: (message: string) => void;
  addNetworkLogMessage: (message: string) => void;
  setBatteryChartTimeframe: (timeframe: BatteryChartTimeframe) => void;
  fetchPublicIpAddress: () => Promise<void>; // Added action for public IP
  updateEstimatedShutdownTime: () => void;
  updatePowerState: (powerState: Battery.PowerState) => void;
  // setScreenBrightness: (level: number) => void; // Removed unused action
  setNetInfo: (netInfoState: NetInfoState) => void;
  // Latency Test Actions
  setLatencyMs: (latency: number | null) => void;
  setIsTestingLatency: (isTesting: boolean) => void;
  // Speed Test History Actions
  addSpeedTestResult: (result: { latency: number | null }) => void;
  loadSpeedTestHistory: () => Promise<void>;
  // Display Info Action
  fetchDisplayInfo: () => void; // Added action for display info
}

// --- Constants ---
const MAX_BATTERY_HISTORY_LENGTH = 60 * 24 * 7; // Renamed for clarity
const MAX_SPEED_TEST_HISTORY_LENGTH = 10; // Keep last 10 tests
const LOG_INTERVAL_MS = 60 * 1000;
const BATTERY_HISTORY_KEY = 'batteryHistory';
const SPEED_TEST_HISTORY_KEY = 'speedTestHistory'; // Key for storage
const MAX_LOG_MESSAGES = 50;
const LOW_BATTERY_THRESHOLD = 0.4;
const CRITICAL_BATTERY_THRESHOLD = 0.15;

// --- Store Implementation ---
// TODO: Consider refactoring into slices (e.g., createPowerSlice, createNetworkSlice) if store complexity increases further.
export const useSystemInfoStore = create<SystemInfoState>((set, get) => ({
  // Initial State
  batteryLevel: undefined,
  batteryStatus: '--------',
  lowPowerModeEnabled: null,
  batteryHistory: [],
  batteryChartTimeframe: '1h',
  estimatedShutdownTime: null,
  // screenBrightness: null, // Removed unused state
  totalStorage: undefined,
  freeStorage: undefined,
  totalMemory: Device.totalMemory ?? null,
  netInfo: null,
  cpuUsage: undefined,
  cpuSpeed: '---- MHz',
  manufacturer: Device.manufacturer ?? null,
  modelName: Device.modelName ?? null,
  cpuArchitectures: Device.supportedCpuArchitectures ?? null,
  osName: Device.osName ?? null,
  osVersion: Device.osVersion ?? null,
  deviceYearClass: Device.deviceYearClass ?? null,
  // Display Info Initial State
  screenWidth: null,
  screenHeight: null,
  screenScale: null,
  // Public IP Initial State
  publicIpAddress: null,
  // Latency Test Initial State
  latencyMs: null,
  isTestingLatency: false,
  // Speed Test History Initial State
  speedTestHistory: [],
  // Log Messages Initial State
  logMessages: [
    '> MONITORING ACTIVE...',
    '> AEGIS CORE SYNC ESTABLISHED... OK',
    '> SYSTEM BOOT INITIALIZED... OK',
    '> SYSTEM PARAMETERS SCANNING...',
    '> CPU TEMP: SENSOR INTERFACE N/A (V1)',
    '> ADVANCED TELEMETRY: OFFLINE (V1)',
  ].reverse(),
  storageLogMessages: [
    '> STORAGE SUBSYSTEM ONLINE',
    '> INTEGRITY CHECK PASSED (INITIAL)',
    '> FILE SYSTEM INITIALIZED... OK.',
  ].reverse(),
   powerLogMessages: [
    '> POWER CORE ONLINE',
    '> OUTPUT LEVELS NOMINAL',
    '> BATTERY REMAINS CHARGED',
    '> DISCHARGING IN PROCESS',
    '> BATTERY VOLTAGE STABLE',
    '> BATTERY OUTPUT WITHIN SPECIFICATIONS',
  ].reverse(),
  networkLogMessages: [ // Added initial network logs
    '> NETWORK INTERFACE ONLINE',
    '> MONITORING LINK STATUS...',
    '> READY FOR DATA TRANSMISSION',
    '> DATA TRANSMISSION COMMENCING......SUCCESS',
    '> NETWORK VECTOR STATUS: ONLINE',
    '> NETWORK CONNECTION ESTABLISHED... OK',
    '> NETWORK TYPE CHANGED: WIRELESS',
  ].reverse(),


  // --- Actions ---

  fetchInitialData: async () => {
          if (__DEV__) { console.log('[Store] Fetching initial data...'); }
    try {
      // Load Battery History
      const batteryJsonValue = await AsyncStorage.getItem(BATTERY_HISTORY_KEY);
      let loadedBatteryHistory: BatteryHistoryEntry[] = [];
       if (batteryJsonValue != null) {
        try {
            loadedBatteryHistory = JSON.parse(batteryJsonValue);
             if (!Array.isArray(loadedBatteryHistory) || (loadedBatteryHistory.length > 0 && (typeof loadedBatteryHistory[0]?.timestamp !== 'number' || typeof loadedBatteryHistory[0]?.level !== 'number'))) {
                 console.warn("[Store] Invalid battery history format found in AsyncStorage. Resetting.");
                 loadedBatteryHistory = [];
                 await AsyncStorage.removeItem(BATTERY_HISTORY_KEY);
             } else {
                 const weekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
                 loadedBatteryHistory = loadedBatteryHistory.filter(entry => entry.timestamp >= weekAgo);
                 if (__DEV__) { console.log(`[Store] Battery history loaded. ${loadedBatteryHistory.length} entries.`); }
             }
        } catch (e) {
            console.error("[Store] Failed to parse battery history from AsyncStorage:", e);
            loadedBatteryHistory = [];
            await AsyncStorage.removeItem(BATTERY_HISTORY_KEY);
        }
      } else {
                  if (__DEV__) { console.log("[Store] No previous battery history found."); }
      }
      set({ batteryHistory: loadedBatteryHistory });

      // Load Speed Test History
      await get().loadSpeedTestHistory(); // Call action to load speed test history

      // Fetch Live Data
      const level = await Battery.getBatteryLevelAsync();
      const powerState = await Battery.getPowerStateAsync();
      const totalDisk = await FileSystem.getTotalDiskCapacityAsync();
      const freeDisk = await FileSystem.getFreeDiskStorageAsync();

      // Set Initial State
      get().updatePowerState(powerState);
      set({
        batteryLevel: level,
        totalStorage: totalDisk,
        freeStorage: freeDisk,
      });
       if (level !== undefined && level < LOW_BATTERY_THRESHOLD) {
           get().addPowerLogMessage(`WARN: BATTERY LEVEL BELOW ${LOW_BATTERY_THRESHOLD * 100}%`);
       }

      if (__DEV__) { console.log('[Store] Initial data fetched and set.'); }
      get().updateEstimatedShutdownTime();
      get().fetchDisplayInfo(); // Fetch display info on initial load

    } catch (error) {
      console.error("[Store] Error fetching initial data:", error);
      set({ batteryStatus: 'ERROR' });
      get().addLogMessage('> ERROR: FAILED TO FETCH INITIAL DATA');
    }
  },

  logBatteryHistory: async () => {
    try {
      const currentLevel = await Battery.getBatteryLevelAsync();
      if (currentLevel !== undefined && currentLevel !== null) {
        const now = Date.now();
        const newEntry = { timestamp: now, level: currentLevel };
        const prevState = get();

        if (currentLevel < LOW_BATTERY_THRESHOLD && (prevState.batteryLevel === undefined || prevState.batteryLevel >= LOW_BATTERY_THRESHOLD)) {
             get().addPowerLogMessage(`WARN: BATTERY LEVEL BELOW ${LOW_BATTERY_THRESHOLD * 100}%`);
        }

        const weekAgo = now - 7 * 24 * 60 * 60 * 1000;
        const updatedBatteryHistory = [...prevState.batteryHistory, newEntry]
                                .filter(entry => entry.timestamp >= weekAgo)
                                .slice(-MAX_BATTERY_HISTORY_LENGTH); // Use renamed constant

        set({ batteryHistory: updatedBatteryHistory, batteryLevel: currentLevel });
        AsyncStorage.setItem(BATTERY_HISTORY_KEY, JSON.stringify(updatedBatteryHistory)).catch(e => console.error("Failed to save battery history:", e));
        get().updateEstimatedShutdownTime();
      }
    } catch (error) {
      console.error("[Store] Error logging battery level:", error);
    }
  },

  addLogMessage: (message: string) => { set(state => ({ logMessages: [message, ...state.logMessages].slice(0, MAX_LOG_MESSAGES) })); },
  addStorageLogMessage: (message: string) => { set(state => ({ storageLogMessages: [message, ...state.storageLogMessages].slice(0, MAX_LOG_MESSAGES) })); },
  addPowerLogMessage: (message: string) => { set(state => ({ powerLogMessages: [message, ...state.powerLogMessages].slice(0, MAX_LOG_MESSAGES) })); },
  addNetworkLogMessage: (message: string) => { set(state => ({ networkLogMessages: [message, ...state.networkLogMessages].slice(0, MAX_LOG_MESSAGES) })); },

  setBatteryChartTimeframe: (timeframe: BatteryChartTimeframe) => {
    set({ batteryChartTimeframe: timeframe });
    get().addPowerLogMessage(`> CHART TIMEFRAME SET TO: ${timeframe.toUpperCase()}`);
  },

  // TODO: Review complexity/accuracy of shutdown time estimation logic.
  updateEstimatedShutdownTime: () => {
      const { batteryHistory, batteryLevel, batteryStatus, addPowerLogMessage, estimatedShutdownTime } = get();
      const now = Date.now();
      let newEstimate: number | null = null;
      let logMessage: string | null = null;

      if (batteryStatus === 'DISCHARGING' && batteryHistory.length >= 5 && batteryLevel !== undefined) {
          const relevantHistory = batteryHistory.slice(-6);
          if (relevantHistory.length >= 2) {
              const first = relevantHistory[0];
              const last = relevantHistory[relevantHistory.length - 1];
              const durationMs = last.timestamp - first.timestamp;
              const levelChange = last.level - first.level;

              if (durationMs > 10000 && levelChange < -0.001) {
                  const dischargeRatePerMs = levelChange / durationMs;
                  const remainingMs = (batteryLevel / Math.abs(dischargeRatePerMs));

                  if (remainingMs > 0 && remainingMs < 48 * 60 * 60 * 1000) {
                      newEstimate = now + remainingMs;
                      const criticalTimeMs = CRITICAL_BATTERY_THRESHOLD * 60 * 1000;
                      const wasLowRecently = estimatedShutdownTime !== null && (estimatedShutdownTime - now < criticalTimeMs * 1.2);
                      if (remainingMs < criticalTimeMs && !wasLowRecently) {
                          const minsLeft = Math.floor(remainingMs / 60000);
                          logMessage = `!!! CRITICAL ALERT: T-${minsLeft} MIN TO TOTAL SHUTDOWN !!!`;
                      }
                  }
              }
          }
      }
      const currentEstimate = get().estimatedShutdownTime;
      const diff = newEstimate !== null && currentEstimate !== null ? Math.abs(newEstimate - currentEstimate) : Infinity;
      if (newEstimate !== currentEstimate && (newEstimate === null || currentEstimate === null || diff > 60000)) {
          set({ estimatedShutdownTime: newEstimate });
      }
      if (logMessage) { addPowerLogMessage(logMessage); }
  },

  updatePowerState: (powerState: Battery.PowerState) => {
      const { batteryStatus: prevStatus, lowPowerModeEnabled: prevLowPower, addPowerLogMessage, updateEstimatedShutdownTime, batteryLevel: prevLevel } = get();
      const { batteryState, lowPowerMode, batteryLevel: levelFromEvent } = powerState;

      let statusString = 'UNKNOWN';
      switch (batteryState) {
        case Battery.BatteryState.CHARGING: statusString = 'CHARGING'; break;
        case Battery.BatteryState.UNPLUGGED: statusString = 'DISCHARGING'; break;
        case Battery.BatteryState.FULL: statusString = 'FULL'; break;
        case Battery.BatteryState.UNKNOWN: statusString = 'UNKNOWN'; break;
      }

      const statusChanged = statusString !== prevStatus;
      const lowPowerChanged = lowPowerMode !== prevLowPower;
      const levelChanged = levelFromEvent !== undefined && levelFromEvent !== prevLevel;

      if (statusChanged && prevStatus !== '--------') { addPowerLogMessage(`> POWER STATUS CHANGE: ${statusString}`); }
      if (lowPowerChanged && prevLowPower !== null) { addPowerLogMessage(`> ${lowPowerMode ? 'EMERGENCY BATTERY COOLING ACTIVATED' : 'LOW POWER MODE DEACTIVATED'}`); }
      const currentLevel = levelFromEvent ?? prevLevel;
      if (currentLevel !== undefined && currentLevel < LOW_BATTERY_THRESHOLD && (prevLevel === undefined || prevLevel >= LOW_BATTERY_THRESHOLD)) { addPowerLogMessage(`WARN: BATTERY LEVEL BELOW ${LOW_BATTERY_THRESHOLD * 100}%`); }

      const stateUpdate: Partial<SystemInfoState> = {};
      if (statusChanged) stateUpdate.batteryStatus = statusString;
      if (lowPowerChanged) stateUpdate.lowPowerModeEnabled = lowPowerMode;
      if (levelChanged) stateUpdate.batteryLevel = levelFromEvent;

      if (statusChanged || lowPowerChanged || levelChanged) {
          set(stateUpdate);
          if (statusChanged && (statusString === 'DISCHARGING' || prevStatus === 'DISCHARGING')) { updateEstimatedShutdownTime(); }
      }
  },

  // Removed unused setScreenBrightness action

  setNetInfo: (netInfoState: NetInfoState) => {
    const previousState = get().netInfo;
    set({ netInfo: netInfoState });

    if (previousState?.isConnected !== null && netInfoState.isConnected !== null && netInfoState.isConnected !== previousState?.isConnected) {
      const status = netInfoState.isConnected ? 'ONLINE' : 'OFFLINE';
      // Use the new network log action
      get().addNetworkLogMessage(`> NETWORK VECTOR STATUS: ${status}`);
    }
     // Log type changes too
     if (previousState?.type && netInfoState.type && netInfoState.type !== previousState.type) {
       get().addNetworkLogMessage(`> NETWORK TYPE CHANGED: ${netInfoState.type.toUpperCase()}`);
     }
  },

  // --- Public IP Fetch Action ---
  fetchPublicIpAddress: async () => {
    const { addNetworkLogMessage } = get();
    try {
      addNetworkLogMessage('> FETCHING EXTERNAL ADDRESS...');
      const response = await fetch('https://api.ipify.org?format=json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      const ip = data?.ip;
      if (typeof ip === 'string') {
        set({ publicIpAddress: ip });
        addNetworkLogMessage(`> EXTERNAL ADDRESS ACQUIRED: ${ip}`);
      } else {
        throw new Error('Invalid response format from ipify API');
      }
    } catch (error: any) {
      console.error("[Store] Error fetching public IP:", error);
      set({ publicIpAddress: 'ERROR' }); // Indicate error state
      addNetworkLogMessage(`> ERROR: FAILED TO FETCH EXTERNAL ADDRESS (${error.message || 'Unknown error'})`);
    }
  },


  // --- Latency Test Action Definitions ---
  setLatencyMs: (latency: number | null) => set({ latencyMs: latency }),
  setIsTestingLatency: (isTesting: boolean) => set({ isTestingLatency: isTesting }),

  // --- Speed Test History Action Definitions ---
  loadSpeedTestHistory: async () => {
    try {
      const jsonValue = await AsyncStorage.getItem(SPEED_TEST_HISTORY_KEY);
      let loadedHistory: SystemInfoState['speedTestHistory'] = [];
      if (jsonValue != null) {
        try {
          loadedHistory = JSON.parse(jsonValue);
          // Basic validation (check for latency, but timestamp is primary)
          if (!Array.isArray(loadedHistory) || (loadedHistory.length > 0 && (typeof loadedHistory[0]?.timestamp !== 'number' || typeof loadedHistory[0]?.latency === 'undefined'))) {
            console.warn("[Store] Invalid speed test history format found. Resetting.");
            loadedHistory = [];
            await AsyncStorage.removeItem(SPEED_TEST_HISTORY_KEY);
          } else {
             // Keep only the last N results
             loadedHistory = loadedHistory.slice(-MAX_SPEED_TEST_HISTORY_LENGTH);
             if (__DEV__) { console.log(`[Store] Speed test history loaded. ${loadedHistory.length} entries.`); }
          }
        } catch (e) {
          console.error("[Store] Failed to parse speed test history:", e);
          loadedHistory = [];
          await AsyncStorage.removeItem(SPEED_TEST_HISTORY_KEY);
        }
      } else {
                   if (__DEV__) { console.log("[Store] No previous speed test history found."); }
      }
      set({ speedTestHistory: loadedHistory });
    } catch (error) {
      console.error("[Store] Error loading speed test history:", error);
    }
  },
  addSpeedTestResult: (result: { latency: number | null }) => {
    const newEntry = { timestamp: Date.now(), latency: result.latency };
    set(state => {
      const updatedHistory = [...state.speedTestHistory, newEntry].slice(-MAX_SPEED_TEST_HISTORY_LENGTH);
      AsyncStorage.setItem(SPEED_TEST_HISTORY_KEY, JSON.stringify(updatedHistory)).catch(e => console.error("Failed to save speed test history:", e));
      return { speedTestHistory: updatedHistory };
    });
  },

  // --- Display Info Action Implementation ---
  fetchDisplayInfo: () => {
    try {
      const { width, height, scale } = Dimensions.get('screen');
      set({
        screenWidth: width,
        screenHeight: height,
        screenScale: scale,
      });
      // console.log(`[Store] Display Info: ${width}x${height} @${scale}x`);
    } catch (error) {
      console.error("[Store] Error fetching display info:", error);
      set({ screenWidth: null, screenHeight: null, screenScale: null });
      get().addLogMessage('> ERROR: FAILED TO FETCH DISPLAY INFO');
    }
  },
}));
