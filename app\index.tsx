import React, { useState, useEffect, Suspense } from "react";

import { View, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNetInfo } from "@react-native-community/netinfo";
import { Canvas } from "@react-three/fiber/native";
import { formatBytes } from "../lib/utils";
import { AppHeader } from "../components/layout/AppHeader";
import { AppFooter } from "../components/layout/AppFooter";
import { ScanlineOverlay } from "../components/layout/ScanlineOverlay";
import { StylizedProgressBar } from "../components/ui/StylizedProgressBar";
import { StatLine } from "../components/ui/StatLine";
import { BatteryIcon } from "../components/icons/BatteryIcon";
import { WifiIcon } from "../components/icons/WifiIcon";
import { CellularIcon } from "../components/icons/CellularIcon";
import { StorageIcon } from "../components/icons/StorageIcon";
import { StatSection } from "../components/sections/StatSection";
import { ScrollingLogSection } from "../components/sections/ScrollingLogSection";
import { CoreModel } from "../components/core/CoreModel";
import { GlitchOverlay } from "../components/layout/GlitchOverlay";
import { useSystemInfoStore } from "../store/systemInfoStore";
import * as Battery from "expo-battery";

const LOG_INTERVAL_MS = 60 * 1000;

export default function IndexScreen() {
  const {
    batteryLevel,
    batteryStatus,
    totalStorage,
    freeStorage,
    totalMemory,
    logMessages,
    manufacturer,
    modelName,
    osName,
    osVersion,
    fetchInitialData,
    fetchPublicIpAddress,
    logBatteryHistory,
    addLogMessage,
    setNetInfo,
  } = useSystemInfoStore();

  const [isGlitching, setIsGlitching] = useState(false);
  const netInfo = useNetInfo();

  const abstractLogPool = [
    "> ANALYZING WAVE PATTERN...",
    "> SYNCH RATE: 98.7%",
    "> CORE TEMP STABLE.",
    "> LCL BARRIER INTEGRITY: NOMINAL.",
    "> ROUTING POWER TO SECTOR 3.",
    "> STANDBY FOR DIRECTIVE.",
    "> MAGI CONSENSUS: PENDING...",
    "> CHECKING PERIMETER DEFENSES...",
    "> DATA STREAM NORMAL.",
    "> ENERGY LEVELS OPTIMAL.",
  ];

  useEffect(() => {
    let isMounted = true;

    const loadAndSetupIntervals = async () => {
      setIsGlitching(true);
      await fetchInitialData();
      if (!isMounted) return;

      if (useSystemInfoStore.getState().netInfo?.isConnected) {
        await fetchPublicIpAddress();
        if (!isMounted) return;
      }

      setIsGlitching(false);

      const messageTriggerInterval = setInterval(() => {
        if (!isMounted) return;
        const nextLog =
          abstractLogPool[Math.floor(Math.random() * abstractLogPool.length)];
        addLogMessage(nextLog);
      }, 10000);

      const historyLogInterval = setInterval(() => {
        if (!isMounted) return;
        logBatteryHistory();
      }, LOG_INTERVAL_MS);

      return () => {
        isMounted = false;
        clearInterval(messageTriggerInterval);
        clearInterval(historyLogInterval);
      };
    };

    loadAndSetupIntervals();

    return () => {
      isMounted = false;
    };
  }, [
    fetchInitialData,
    fetchPublicIpAddress,
    logBatteryHistory,
    addLogMessage,
  ]);

  useEffect(() => {
    const previousIsConnected =
      useSystemInfoStore.getState().netInfo?.isConnected;
    if (netInfo) {
      setNetInfo(netInfo);
      if (netInfo.isConnected && !previousIsConnected) {
        fetchPublicIpAddress();
      }
    }
  }, [netInfo, setNetInfo, fetchPublicIpAddress]);

  useEffect(() => {
    const batteryStateSubscription = Battery.addBatteryStateListener(
      async (event) => {
        if (__DEV__) {
          console.log(
            "[Battery Listener] State changed event received:",
            event.batteryState
          );
        }
        const powerState = await Battery.getPowerStateAsync();
        useSystemInfoStore.getState().updatePowerState(powerState);
      }
    );

    return () => {
      batteryStateSubscription.remove();
    };
  }, []);

  useEffect(() => {
    if (
      batteryStatus &&
      batteryStatus !== "--------" &&
      batteryStatus !== "ERROR"
    ) {
      let message = `> POWER CORE STATUS: ${batteryStatus}`;
      if (batteryLevel !== undefined && batteryLevel < 0.2) {
        message += " - LEVEL CRITICAL";
      }
      addLogMessage(message);
    }
  }, [batteryStatus, batteryLevel, addLogMessage]);

  const storageUsedPercentage =
    totalStorage && freeStorage && totalStorage > 0
      ? (totalStorage - freeStorage) / totalStorage
      : 0;
  const batteryPercentage = batteryLevel !== undefined ? batteryLevel * 100 : 0;

  return (
    <SafeAreaView className="flex-1 bg-black" edges={["top", "left", "right"]}>
      <ScanlineOverlay />
      <GlitchOverlay isGlitching={isGlitching} />
      <AppHeader />
      <View className="flex-1 p-3">
        <View className="h-48 mb-3">
          <Suspense fallback={null}>
            <Canvas
              camera={{ position: [0, 0, 3.5], fov: 50 }}
              gl={__DEV__ ? { debug: { checkShaderErrors: false, onShaderError: null } } : undefined}
              onCreated={({ gl }) => {
                gl.domElement.addEventListener('webglcontextlost', () => {
                  console.warn('Main Canvas: WebGL context lost');
                });
                gl.domElement.addEventListener('webglcontextrestored', () => {
                  console.warn('Main Canvas: WebGL context restored');
                });
              }}
            >
              <ambientLight intensity={0.4} />
              <CoreModel
                powerLevel={batteryLevel}
                powerStatus={batteryStatus}
                isConnected={netInfo.isConnected ?? null}
                unitId={modelName ?? "N/A"}
              />
            </Canvas>
          </Suspense>
        </View>
        <ScrollView className="flex-1">
          <StatSection
            title="POWER CORE"
            isAlerting={batteryLevel !== undefined && batteryLevel < 0.2}
            routeName="/power"
            showIndicator={true}
          >
            <StatLine
              label="Level"
              value={
                batteryPercentage ? `${batteryPercentage.toFixed(0)} %` : "-- %"
              }
              icon={
                <BatteryIcon
                  level={batteryLevel}
                  status={batteryStatus}
                  color="#00BFFF"
                />
              }
            />
            <StylizedProgressBar percentage={batteryLevel} />
          </StatSection>

          <StatSection
            title="DATA STORAGE"
            routeName="/storage"
            showIndicator={true}
          >
            <StatLine
              label="Used"
              value={formatBytes(
                typeof totalStorage === "number" &&
                  typeof freeStorage === "number"
                  ? totalStorage - freeStorage
                  : undefined
              )}
              icon={<StorageIcon />}
            />
            <StylizedProgressBar
              percentage={storageUsedPercentage}
              alertThreshold={0.9}
            />
          </StatSection>

          <StatSection title="SYSTEM SPECS" showIndicator={false}>
            <StatLine
              label="AEGIS OS"
              value={`${osName ?? "Unknown"} ${osVersion ?? ""}`}
            />
            <StatLine
              label="Unit Type"
              value={`${manufacturer ?? "Unknown"} ${modelName ?? ""}`}
            />
            <StatLine
              label="Main Memory"
              value={totalMemory ? formatBytes(totalMemory) : "---- GB"}
            />
          </StatSection>

          <StatSection
            title="NETWORK VECTOR"
            routeName="/network"
            showIndicator={true}
          >
            <StatLine
              label="Type"
              value={netInfo.type?.toUpperCase() ?? "UNKNOWN"}
              icon={
                netInfo.type === "wifi" ? (
                  <WifiIcon />
                ) : netInfo.type === "cellular" ? (
                  <CellularIcon />
                ) : undefined
              }
            />
            <StatLine
              label="Status"
              value={
                netInfo.isConnected === null
                  ? "--"
                  : netInfo.isConnected
                  ? "ONLINE"
                  : "OFFLINE"
              }
              valueColor={
                netInfo.isConnected === false
                  ? "text-aegis-red"
                  : "text-aegis-cyan"
              }
            />
          </StatSection>

          <ScrollingLogSection
            title="SYSTEM EVENT LOG"
            logMessages={logMessages}
            lineHeight={50}
          />
        </ScrollView>
      </View>
      <AppFooter />
    </SafeAreaView>
  );
}
