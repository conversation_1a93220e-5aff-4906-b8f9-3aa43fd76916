import React, { Suspense, useState, useEffect } from "react";
import { View, Text, ScrollView, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import * as Haptics from "expo-haptics";
import { useSystemInfoStore, SystemInfoState } from "../store/systemInfoStore";
import { StatSection } from "../components/sections/StatSection";
import { StatLine } from "../components/ui/StatLine";
import { StylizedProgressBar } from "../components/ui/StylizedProgressBar";
import { BatteryIcon } from "../components/icons/BatteryIcon";
import { BatteryChart } from "../components/charts/BatteryChart";
import { formatTimeRemaining } from "../lib/utils";
import { ScanlineOverlay } from "../components/layout/ScanlineOverlay";
import { GlitchOverlay } from "../components/layout/GlitchOverlay";
import { ScrollingLogSection } from "../components/sections/ScrollingLogSection";
import { Power3dModel } from "../components/core/Power3dModel";
import { Canvas } from "@react-three/fiber/native";

type Timeframe = SystemInfoState["batteryChartTimeframe"];

export default function PowerDetailScreen() {
  const {
    batteryLevel,
    batteryStatus,
    batteryHistory,
    powerLogMessages,
    lowPowerModeEnabled,
    batteryChartTimeframe,
    setBatteryChartTimeframe,
    estimatedShutdownTime,
    deviceYearClass,
  } = useSystemInfoStore();
  const batteryPercentage = batteryLevel !== undefined ? batteryLevel * 100 : 0;
  const isCriticallyLow = batteryLevel !== undefined && batteryLevel < 0.2;

  const CRITICAL_BATTERY_THRESHOLD = 0.15;
  const LOW_BATTERY_THRESHOLD = 0.4;

  let outputStatusValue = "NOMINAL";
  let outputStatusColor = "text-aegis-cyan";
  let outputStatusGlow: 'cyan' | 'orange' | 'red' | undefined = 'cyan';

  if (batteryStatus === 'CHARGING') {
    outputStatusValue = "CHARGING";
    outputStatusColor = "text-green-400";
    outputStatusGlow = undefined;
  } else if (batteryStatus === 'FULL') {
    outputStatusValue = "MAX CAPACITY";
    outputStatusColor = "text-aegis-cyan";
    outputStatusGlow = 'cyan';
  } else if (lowPowerModeEnabled) {
    outputStatusValue = "CONSERVATION MODE";
    outputStatusColor = "text-yellow-500";
    outputStatusGlow = 'orange';
  } else if (batteryLevel !== undefined && batteryLevel < CRITICAL_BATTERY_THRESHOLD) {
    outputStatusValue = "OUTPUT CRITICAL";
    outputStatusColor = "text-aegis-red";
    outputStatusGlow = 'red';
  } else if (batteryLevel !== undefined && batteryLevel < LOW_BATTERY_THRESHOLD) {
    outputStatusValue = "OUTPUT LOW";
    outputStatusColor = "text-aegis-orange";
    outputStatusGlow = 'orange';
  }

  let cellAgeValue = "MODERATE";
  let cellAgeColor = "text-aegis-cyan";
  let cellAgeGlow: 'cyan' | 'orange' | 'red' | undefined = 'cyan';
  const currentYear = new Date().getFullYear();

  if (deviceYearClass) {
    if (deviceYearClass >= currentYear - 1) {
      cellAgeValue = "NEW";
      cellAgeColor = "text-green-400";
      cellAgeGlow = undefined;
    } else if (deviceYearClass >= currentYear - 3) {
      cellAgeValue = "AVERAGE";
    } else {
      cellAgeValue = "OLDER";
      cellAgeColor = "text-aegis-orange";
      cellAgeGlow = 'orange';
    }
  } else {
    cellAgeValue = "UNKNOWN";
    cellAgeColor = "text-gray-500";
    cellAgeGlow = undefined;
  }

  const [isGlitching, setIsGlitching] = useState(false);

  useEffect(() => {
    setIsGlitching(true);
    const timer = setTimeout(() => setIsGlitching(false), 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-black">
      <Stack.Screen
        options={{
          headerShown: true,
          headerStyle: { backgroundColor: "#000000" },
          headerTintColor: "#00BFFF",
          headerTitleStyle: { fontFamily: "Orbitron-Bold", color: "#FF6600" },
          title: "POWER DETAIL",
          headerBackTitle: "Back",
        }}
      />
      <ScanlineOverlay />
      <GlitchOverlay isGlitching={isGlitching} />
      <View className="flex-1 p-3">
        <View className="flex-[0.6] mb-3 border border-aegis-cyan/20">
          <Suspense fallback={null}>
            <Canvas
              camera={{ position: [0, 0, 4.5], fov: 50 }}
              gl={__DEV__ ? { debug: { checkShaderErrors: false, onShaderError: null } } : undefined}
            >
              <ambientLight intensity={0.4} />
              <directionalLight position={[3, 4, 5]} intensity={0.8} />
              <Power3dModel
                batteryLevel={batteryLevel}
                batteryStatus={batteryStatus}
              />
            </Canvas>
          </Suspense>
        </View>
        <View className="">
          <ScrollingLogSection
            title="POWER CONSUMPTION LOG"
            lineHeight={20}
            logMessages={powerLogMessages}
          />
        </View>
        <ScrollView className="flex-[0.4]">
          <StatSection title="POWER CORE" isAlerting={isCriticallyLow}>
            <StatLine
              label="Level"
              value={
                batteryPercentage ? `${batteryPercentage.toFixed(0)} %` : "-- %"
              }
              icon={
                <BatteryIcon
                  level={batteryLevel}
                  status={batteryStatus}
                  color="#00BFFF"
                />
              }
              valueGlow={isCriticallyLow ? "red" : "cyan"}
              valueColor={isCriticallyLow ? "text-red-500" : "text-aegis-cyan"}
            />
            <StylizedProgressBar percentage={batteryLevel} />
            <StatLine
              label="Status"
              value={batteryStatus}
              valueGlow={isCriticallyLow ? "red" : "cyan"}
              valueColor={isCriticallyLow ? "text-red-500" : "text-aegis-cyan"}
            />
            <StatLine
              label="Est. Shutdown"
              value={
                batteryStatus === "DISCHARGING"
                  ? `T-${formatTimeRemaining(estimatedShutdownTime)}`
                  : "--"
              }
              valueColor={
                estimatedShutdownTime !== null &&
                estimatedShutdownTime - Date.now() < 15 * 60 * 1000
                  ? "text-red-500"
                  : "text-aegis-cyan"
              }
              valueGlow={
                estimatedShutdownTime !== null &&
                estimatedShutdownTime - Date.now() < 15 * 60 * 1000
                  ? "red"
                  : undefined
              }
            />
            <View className="mt-2 border-t border-aegis-cyan/20 pt-1">
              <StatLine
                label="OUTPUT STATUS"
                value={outputStatusValue}
                valueColor={outputStatusColor}
                valueGlow={outputStatusGlow}
              />
              <View className="flex-row items-center justify-between">
                <StatLine
                  label="CELL AGE (EST.)"
                  value={cellAgeValue}
                  valueColor={cellAgeColor}
                  valueGlow={cellAgeGlow}
                />
                <TouchableOpacity
                  onPress={() =>
                    Alert.alert(
                      "Cell Age (Estimated)",
                      "This is an estimation based on the device's manufacturing year class. Actual battery health can vary based on usage patterns and charge cycles."
                    )
                  }
                  className="ml-2 p-1"
                >
                  <Text className="text-aegis-cyan text-sm font-bold">ⓘ</Text>
                </TouchableOpacity>
              </View>
              <StatLine
                label="Low Power Mode"
                value={
                  lowPowerModeEnabled === null
                    ? "--"
                    : lowPowerModeEnabled
                    ? "ACTIVE"
                    : "INACTIVE"
                }
                valueColor={
                  lowPowerModeEnabled ? "text-yellow-500" : "text-gray-500"
                }
              />
            </View>
          </StatSection>

          <View className="flex-row justify-center space-x-2 my-3">
            {(["1h", "6h", "24h"] as const).map((tf) => (
              <TouchableOpacity
                key={tf}
                onPress={() => {
                  setBatteryChartTimeframe(tf);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                className={` px-4 py-1 border-2 ${
                  batteryChartTimeframe === tf
                    ? "bg-aegis-orange/80 border-aegis-orange"
                    : "bg-black/30 border-aegis-cyan/60"
                } `}
              >
                <Text
                  className={` font-title text-sm tracking-wider ${
                    batteryChartTimeframe === tf
                      ? "text-black"
                      : "text-aegis-cyan"
                  } `}
                >
                  {tf.toUpperCase()}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <StatSection title="BATTERY HISTORY">
            {batteryHistory.length > 1 ? (
              <>
                <BatteryChart
                  data={batteryHistory}
                  height={100}
                  timeframe={batteryChartTimeframe}
                />
                <Text className="text-aegis-cyan/60 text-xs font-mono text-right -mt-1">
                  DISCHARGE STATUS FOR THE LAST{" "}
                  {batteryChartTimeframe.toUpperCase()}
                </Text>
              </>
            ) : (
              <View className="h-[100px] flex items-center justify-center">
                <Text className="text-aegis-cyan/50 text-xs font-mono">
                  [ GATHERING HISTORY ]
                </Text>
              </View>
            )}
          </StatSection>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
