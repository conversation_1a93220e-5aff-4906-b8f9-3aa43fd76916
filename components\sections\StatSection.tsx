import React, { useEffect, useRef } from 'react';
import { View, Animated, TouchableOpacity, Text } from 'react-native'; // Import Text
import Svg, { Line, Polygon } from 'react-native-svg';
import { useRouter } from 'expo-router';

// Stat Section with Alert Flash, Custom Divider, and Navigation
export const StatSection = ({ title, children, isAlerting, routeName, showIndicator }: { title: string; children: React.ReactNode; isAlerting?: boolean; routeName?: string; showIndicator?: boolean }) => {
  const router = useRouter();
  const alertAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isAlerting) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(alertAnim, { toValue: 1, duration: 300, useNativeDriver: false }),
          Animated.timing(alertAnim, { toValue: 0, duration: 300, useNativeDriver: false }),
          Animated.delay(200)
        ]),
        { iterations: 3 }
      ).start();
    } else {
      alertAnim.stopAnimation();
      alertAnim.setValue(0);
    }
    return () => alertAnim.stopAnimation();
  }, [isAlerting, alertAnim]);

  const animatedColor = alertAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['#FF6600', '#FF0000']
  });

  const dividerColor = "#FF6600";

  const handlePress = () => {
    if (routeName) {
      router.push(routeName as any);
    }
  };

  // Separate Title Text component
  const TitleText = (
    <Animated.Text
      className="font-title text-lg uppercase"
      style={{
        color: animatedColor,
        textShadowColor: '#FF6600',
        textShadowRadius: 8,
      }}
    >
      {title}
    </Animated.Text>
  );

  // Separate Divider component
  const Divider = (
    <View className="mb-2 h-[2px] flex-row items-center">
      <Svg height="6" width="8" viewBox="0 0 8 6">
        <Polygon points="0,3 8,0 8,6" fill={dividerColor} />
      </Svg>
      <View className="flex-1 h-[1px]" style={{ backgroundColor: dividerColor }} />
      <Svg height="6" width="8" viewBox="0 0 8 6">
        <Polygon points="8,3 0,0 0,6" fill={dividerColor} />
      </Svg>
    </View>
  );

  // Separate Indicator component
  const Indicator = routeName && showIndicator ? (
    <Text className="text-aegis-cyan font-mono text-lg ml-2">{'>'}</Text>
  ) : null;

  return (
    <View className="mb-4">
      {routeName ? (
        <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
          <View className="flex-row items-center mb-1">
            {TitleText}
            {Indicator}
          </View>
          {Divider}
        </TouchableOpacity>
      ) : (
        <>
          <View className="flex-row items-center mb-1">
            {TitleText}
          </View>
          {Divider}
        </>
      )}
      {children}
    </View>
  );
};
