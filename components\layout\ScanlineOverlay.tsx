import React, { useEffect, useRef } from 'react';
import { StyleSheet, Animated, Easing } from 'react-native';
import Svg, { Defs, Pattern, Rect, Line } from 'react-native-svg';

export const ScanlineOverlay = () => {
  const scanlineAnim = useRef(new Animated.Value(0)).current; // Animated value for Y position
  const scanlineHeight = 400; // Height of the scanline pattern area (needs to be larger than screen)
  const scanlineSpacing = 3; // Spacing between scanlines
  const scanlineColor = 'rgba(0, 191, 255, 0.08)'; // Even fainter cyan for scanlines

  useEffect(() => {
    // Loop the animation
    Animated.loop(
      Animated.timing(scanlineAnim, {
        toValue: 1,
        duration: 5000, // Adjust duration for speed
        easing: Easing.linear, // Constant speed
        useNativeDriver: true, // Use native driver for performance
      })
    ).start();
  }, [scanlineAnim]);

  // Interpolate the Y position
  const translateY = scanlineAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, scanlineSpacing], // Move down by one line spacing
  });

  return (
    <Animated.View style={[StyleSheet.absoluteFill, { transform: [{ translateY }] }]}>
      <Svg height={scanlineHeight * 2} width="100%"> 
        <Defs>
          <Pattern
            id="scanlines"
            width={10} 
            height={scanlineSpacing}
            patternUnits="userSpaceOnUse"
          >
            <Line
              x1="0" y1="0" x2="10" y2="0"
              stroke={scanlineColor}
              strokeWidth={1}
            />
          </Pattern>
        </Defs>
        <Rect width="100%" height={scanlineHeight * 2} fill="url(#scanlines)" />
      </Svg>
    </Animated.View>
  );
};
