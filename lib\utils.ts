// --- Helper to format bytes (Added more checks) ---
export const formatBytes = (bytes: number | null | undefined, decimals = 1): string => {
  const defaultUnit = 'GB'; // Default display unit
  const defaultReturn = `-- ${defaultUnit}`;

  if (typeof bytes !== 'number' || isNaN(bytes) || bytes <= 0) {
      return defaultReturn;
  }

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  // Ensure index is within bounds
  if (i < 0 || i >= sizes.length) {
      return defaultReturn;
  }

  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

  if (isNaN(size)) {
      return defaultReturn;
  }

  // Convert lower units to GB for display consistency
  if (sizes[i] === 'MB') return (size / 1024).toFixed(decimals) + ` ${defaultUnit}`;
  if (sizes[i] === 'KB') return (size / 1024 / 1024).toFixed(decimals) + ` ${defaultUnit}`;
  if (sizes[i] === 'Bytes') return (size / 1024 / 1024 / 1024).toFixed(decimals) + ` ${defaultUnit}`;

  // Return formatted size for GB and TB
  return size + ' ' + sizes[i];
};

// Helper for flicker effect (Restored - Used in StatLine.tsx)
export const generateFlicker = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789*&#$%!';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// --- Helper to format remaining time ---
export const formatTimeRemaining = (shutdownTimestamp: number | null): string => {
    if (shutdownTimestamp === null) {
        return '--h --m';
    }
    const now = Date.now();
    const remainingMs = shutdownTimestamp - now;

    if (remainingMs <= 0) {
        return 'SHUTDOWN IMMINENT';
    }

    const totalMinutes = Math.floor(remainingMs / (60 * 1000));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
};

// --- Helper to determine connection quality status ---
export const getConnectionQualityStatus = (
    isConnected: boolean | null | undefined,
    isInternetReachable: boolean | null | undefined,
    signalStrength: number | null // Expecting 0-1
): { status: string; color: 'cyan' | 'orange' | 'red' } => {
    if (isConnected === false) {
        return { status: 'CONNECTION SEVERED', color: 'red' };
    }
    if (isConnected === null || isConnected === undefined) {
        return { status: 'LINK STATUS: UNKNOWN', color: 'orange' };
    }
    // At this point, isConnected is true

    if (isInternetReachable === false) {
        return { status: 'WAN ACCESS: DENIED', color: 'red' };
    }
    if (isInternetReachable === null || isInternetReachable === undefined) {
        return { status: 'WAN ACCESS: UNKNOWN', color: 'orange' };
    }
    // At this point, isConnected and isInternetReachable are true

    if (signalStrength === null) {
        // If connected but no signal strength (e.g., Ethernet), assume optimal
        return { status: 'LINK QUALITY: OPTIMAL', color: 'cyan' };
    }

    if (signalStrength >= 0.7) {
        return { status: 'LINK QUALITY: OPTIMAL', color: 'cyan' };
    } else if (signalStrength >= 0.4) {
        return { status: 'CONNECTION: STABLE', color: 'cyan' };
    } else if (signalStrength >= 0.1) {
        return { status: 'SYNCHRONIZATION: UNSTABLE', color: 'orange' };
    } else {
        return { status: 'SIGNAL CRITICAL', color: 'red' };
    }
    // We could add latency checks here later if needed
};

// --- Helper to format timestamp into relative thematic string ---
export const formatRelativeTimeThematic = (timestamp: number): string => {
    const now = Date.now();
    const diffSeconds = Math.round((now - timestamp) / 1000);

    if (diffSeconds < 5) {
        return 'NOW';
    } else if (diffSeconds < 60) {
        return `T-${diffSeconds}s`;
    } else {
        const diffMinutes = Math.round(diffSeconds / 60);
        return `T-${diffMinutes}m`;
    }
};
