import React, { useEffect, useState, useRef } from 'react';
import { Stack, SplashScreen, useRouter, useRootNavigationState } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useFonts } from 'expo-font';
import { View } from 'react-native';
import mobileAds, {
  BannerAd,
  BannerAdSize,
  TestIds,
  InterstitialAd,
  AdEventType,
} from 'react-native-google-mobile-ads';
import './global.css';

SplashScreen.preventAutoHideAsync();

const interstitialAdUnitId = __DEV__ ? TestIds.INTERSTITIAL : process.env.EXPO_PUBLIC_ADMOB_INTERSTITIAL_ID_ANDROID as string;

const interstitial = InterstitialAd.createForAdRequest(interstitialAdUnitId, {
  requestNonPersonalizedAdsOnly: true,
});

let lastAdShowTime = 0;
const AD_COOLDOWN_MS = 5 * 60 * 1000;

export default function RootLayout() {
  const router = useRouter();
  const navigationState = useRootNavigationState();
  const previousRouteNameRef = useRef<string | null>(null);
  const [fontsLoaded, fontError] = useFonts({
    'SpaceMono-Regular': require('../assets/fonts/SpaceMono-Regular.ttf'),
    'Orbitron-Bold': require('../assets/fonts/Orbitron-VariableFont_wght.ttf'),
  });

  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  useEffect(() => {
    mobileAds()
      .initialize()
      .then(adapterStatuses => {
        if (__DEV__) { console.log('Mobile Ads SDK Initialized:', adapterStatuses); }
      });
  }, []);

  
  const [interstitialLoaded, setInterstitialLoaded] = useState(false);

  useEffect(() => {
    const unsubscribeLoaded = interstitial.addAdEventListener(AdEventType.LOADED, () => {
      if (__DEV__) { console.log('Interstitial Ad Loaded'); }
      setInterstitialLoaded(true);
    });

    const unsubscribeError = interstitial.addAdEventListener(AdEventType.ERROR, (error) => {
      if (__DEV__) { console.error('Interstitial Ad Failed To Load:', error); }
      setInterstitialLoaded(false);
    });

    const unsubscribeClosed = interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      if (__DEV__) { console.log('Interstitial Ad Closed'); }
      setInterstitialLoaded(false);
      interstitial.load();
    });

    interstitial.load();

    return () => {
      unsubscribeLoaded();
      unsubscribeError();
      unsubscribeClosed();
    };
  }, []);

  useEffect(() => {
    if (
      navigationState &&
      navigationState.routes &&
      typeof navigationState.index === 'number' &&
      navigationState.index >= 0 &&
      navigationState.index < navigationState.routes.length
    ) {
      const currentRoute = navigationState.routes[navigationState.index];
      const currentRouteName = currentRoute?.name;
      const previousRouteName = previousRouteNameRef.current;

      const detailScreens = ['power', 'storage', 'network'];

      if (__DEV__) { console.log(`Navigating: ${previousRouteName} -> ${currentRouteName}`); }

      if (
        currentRouteName === 'index' &&
        previousRouteName && detailScreens.includes(previousRouteName) &&
        interstitialLoaded
      ) {
        const now = Date.now();
        if (now - lastAdShowTime > AD_COOLDOWN_MS) {
          if (__DEV__) { console.log('Cooldown passed. Attempting to show Interstitial Ad...'); }
          interstitial.show().catch(error => {
            console.error("Failed to show interstitial ad:", error);
            setInterstitialLoaded(false);
            interstitial.load();
          });
          lastAdShowTime = now;
        } else {
          if (__DEV__) {
            const secondsRemaining = Math.round((AD_COOLDOWN_MS - (now - lastAdShowTime)) / 1000);
            if (__DEV__) { console.log(`Ad skipped due to cooldown. ${secondsRemaining}s remaining.`); }
          }
        }
      }

      previousRouteNameRef.current = currentRouteName;
    }
  }, [navigationState, interstitialLoaded]); // Keep dependencies

  if (!fontsLoaded && !fontError) {
    return null;
  }

  const bannerAdUnitId = __DEV__ ? TestIds.BANNER : process.env.EXPO_PUBLIC_ADMOB_BANNER_ID_ANDROID as string;

  return (
    <SafeAreaProvider className="bg-black">
      <View className="flex-1">
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="index" />
          <Stack.Screen name="power" />
          <Stack.Screen name="storage" />
          <Stack.Screen name="network" />
        </Stack>
        <View className="items-center absolute bottom-0 left-0 right-0">
          <BannerAd
            unitId={bannerAdUnitId}
            size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: true,
            }}
            onAdFailedToLoad={(error) => { if (__DEV__) { console.error('Banner Ad Failed To Load:', error); } }}
          />
        </View>
      </View>
      <StatusBar style="light" />
    </SafeAreaProvider>
  );
}
