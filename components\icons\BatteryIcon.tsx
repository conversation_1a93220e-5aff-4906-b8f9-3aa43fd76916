import React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

// Basic Battery Icon (adjust path data as needed for style)
export const BatteryIcon = ({ level, status, size = 16, color = "#00BFFF" }: { level?: number, status?: string, size?: number, color?: string }) => {
  const fillPercent = level !== undefined ? Math.max(0, Math.min(100, level * 100)) : 50;
  const charging = status === 'CHARGING';
  // Simple path for a battery outline
  const batteryPath = "M1 5 H13 V19 H1 V5 M13 9 H15 V15 H13";
  // Path for a charging bolt
  const boltPath = "M7 8 L10 8 L8 12 L11 12 L6 17 L8 13 L5 13 L7 8";

  return (
    <Svg height={size} width={size * 0.8} viewBox="0 0 16 24">
      <Path d={batteryPath} stroke={color} strokeWidth="1.5" fill="none" />
      <Rect
        x="2"
        y={6 + (12 * (1 - fillPercent / 100))}
        width="10"
        height={12 * (fillPercent / 100)}
        fill={color}
      />
      {!!charging && <Path d={boltPath} fill={color} />}
    </Svg>
  );
};
