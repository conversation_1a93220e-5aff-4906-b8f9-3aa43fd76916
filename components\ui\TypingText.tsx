import React, { useState, useEffect, useRef } from 'react';
import { Text, View } from 'react-native';

interface TypingTextProps {
  text: string;
  speed?: number; // Milliseconds per character
  textClassName?: string; // Allow passing custom text styles
  cursorClassName?: string; // Allow passing custom cursor styles
  onComplete?: () => void; // Callback when typing finishes
}

export const TypingText: React.FC<TypingTextProps> = ({
  text,
  speed = 100, // Default speed: 100ms per char
  textClassName = "text-aegis-cyan font-mono text-sm", // Default styles
  cursorClassName = "text-aegis-cyan font-mono text-sm",
  onComplete,
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [cursorVisible, setCursorVisible] = useState(true);
  const indexRef = useRef(0);
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const cursorIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [restartCounter, setRestartCounter] = useState(0);
  
  // Setup cursor blinking using a regular interval instead of Animated
  useEffect(() => {
    // Cursor blinks every second (500ms on, 500ms off)
    cursorIntervalRef.current = setInterval(() => {
      setCursorVisible(visible => !visible);
    }, 300);
    
    // Cleanup on unmount
    return () => {
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
        cursorIntervalRef.current = null;
      }
    };
  }, []);
  
  // Typing effect
  useEffect(() => {
    // Reset state for a new typing sequence
    indexRef.current = 0;
    setDisplayedText('');
    
    // Clear any existing intervals
    if (typingIntervalRef.current) {
      clearInterval(typingIntervalRef.current);
    }
    
    // Start typing animation
    typingIntervalRef.current = setInterval(() => {
      if (indexRef.current < text.length) {
        // Safely append the next character
        const nextChar = text.charAt(indexRef.current);
        setDisplayedText(prev => prev + nextChar);
        indexRef.current += 1;
      } else {
        // Typing complete
        clearInterval(typingIntervalRef.current!);
        typingIntervalRef.current = null;
        
        // Call completion callback
        onComplete?.();
        
        // Schedule restart after a pause
        const pauseDuration = 5000; // 5 seconds pause before restarting
        
        if (restartTimeoutRef.current) {
          clearTimeout(restartTimeoutRef.current);
        }
        
        restartTimeoutRef.current = setTimeout(() => {
          setRestartCounter(count => count + 1); // Trigger effect rerun
        }, pauseDuration);
      }
    }, speed);
    
    // Cleanup
    return () => {
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
        typingIntervalRef.current = null;
      }
      if (restartTimeoutRef.current) {
        clearTimeout(restartTimeoutRef.current);
        restartTimeoutRef.current = null;
      }
    };
  }, [text, speed, onComplete, restartCounter]);
  
  return (
    <View style={{ flexDirection: 'row' }}>
      <Text className={textClassName}>
        {displayedText}
      </Text>
      <Text className={cursorClassName}>
        {cursorVisible ? '█' : ' '}
      </Text>
    </View>
  );
};