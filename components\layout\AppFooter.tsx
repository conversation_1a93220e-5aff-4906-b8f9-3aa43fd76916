import React from 'react'; // Removed useState, useEffect
import { View, Text } from 'react-native';
import { TypingText } from '../ui/TypingText';
import { CurrentTimeDisplay } from '../ui/CurrentTimeDisplay'; // Import the new time component

// Removed formatDateTime helper and time-related state/effect

export const AppFooter = () => {
    // Removed time state and effect

    return (
      <View className="h-8 px-4 flex-row justify-between items-center border-t border-aegis-orange/50">
        <Text className="text-aegis-orange font-title text-base" style={{ textShadowColor: '#FF6600', textShadowRadius: 5 }}>AEGIS{'>'}</Text>{/* Removed trailing space */}
      
        <View className="flex-1 px-2">
            <TypingText
                text="SYSTEM VITALS MONITOR" 
                speed={80} 
                textClassName="text-aegis-cyan font-mono text-sm" 
                cursorClassName="text-aegis-cyan font-mono text-sm" 
            />
        </View>

        
        <CurrentTimeDisplay />
      </View>
    );
};
