/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}" // Add components directory
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        'aegis-orange': '#FF6600', // NGE Orange
        'aegis-red': '#FF0000',    // Alert Red
        'aegis-cyan': '#00BFFF',   // Deep Sky Blue for data/wireframes
        // Black and White are default Tailwind colors (black, white)
        // Add secondary colors later if needed (blue, green, etc.)
      },
      fontFamily: {
        mono: ['SpaceMono-Regular', 'monospace'], // Use SpaceMono as the default mono font
        sans: ['System', 'sans-serif'], // Keep system default for sans-serif for now
        title: ['Orbitron-Bold', 'sans-serif'], // Add Orbitron-Bold for titles
      },
      textShadow: {
        'glow-orange': '0 0 8px #FF6600', // Aegis Orange glow
        'glow-cyan': '0 0 8px #00BFFF',   // Aegis Cyan glow
        'glow-red': '0 0 10px #FF0000',   // Aegis Red glow (slightly larger blur)
        'DEFAULT': 'none', // Default no shadow
      },
    },
  },
  plugins: [
    // textshadow plugin is included in nativewind v4 preset
  ],
}
