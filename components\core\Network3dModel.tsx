import React, { useRef, useMemo, useEffect, useState, useCallback } from "react";
import { useFrame } from "@react-three/fiber/native";
import * as THREE from "three";
import {
  Points,
  PointMaterial,
  Line as DreiLine,
  Grid,
} from "@react-three/drei/native";

// Define props for the new model
interface Network3dModelProps {
  isConnected?: boolean | null;
  isInternetReachable?: boolean | null;
  networkType?: string | null; // TODO: networkType prop is unused
  signalStrength?: number | null;
  latencyMs?: number | null;
  startDelay?: number; // New prop for animation delay in ms
}

// Colors
const DOWNLOAD_COLOR = "#00BFFF";
const UPLOAD_COLOR = "#FF6600";
const LATENCY_COLOR = "#FFFFFF";
const DISCONNECTED_COLOR = "#FF0000";
const UNREACHABLE_COLOR = "#FFA500";

// Constants
const MAX_PARTICLES_PER_TUBE = 50;
const BASE_PARTICLE_SPEED = 0.2;
const LATENCY_JITTER_AMOUNT = 0.04;
const LATENCY_MAX_THRESHOLD = 300;
const NUM_TUBE_LAYERS = 3;
const TUBE_RADII = [0.04, 0.04, 0.04];
const TUBE_Y_OFFSET = 0.05;
const BASE_WAVE_SPEED = 0.5;
const DEFAULT_START_DELAY = 500;
const TUBE_SEGMENTS = 64; // Tubular segments for TubeGeometry
const TUBE_RADIAL_SEGMENTS = 6; // Radial segments for TubeGeometry

// Curve definition helper with direct phase property access
class SineWaveCurve extends THREE.Curve<THREE.Vector3> {
  scale: number;
  amplitude: number;
  frequency: number;
  phase: number;
  offsetY: number;
  private _cachedFrames: { tangents: THREE.Vector3[]; normals: THREE.Vector3[]; binormals: THREE.Vector3[]; } | null = null;
  private _lastPhase: number = -1;

  constructor(
    scale = 1,
    amplitude = 0.5,
    frequency = 1,
    phase = 0,
    offsetY = 0
  ) {
    super();
    this.scale = scale;
    this.amplitude = amplitude;
    this.frequency = frequency;
    this.phase = phase;
    this.offsetY = offsetY;
  }

  getPoint(t: number, optionalTarget = new THREE.Vector3()) {
    const tx = t * 3 - 1.5;
    const ty =
      Math.sin(2 * Math.PI * this.frequency * t + this.phase) * this.amplitude +
      this.offsetY;
    const tz = 0;
    return optionalTarget.set(tx, ty, tz).multiplyScalar(this.scale);
  }

  // Cache Frenet frames computation
  computeFrenetFrames(segments: number, closed?: boolean): { tangents: THREE.Vector3[]; normals: THREE.Vector3[]; binormals: THREE.Vector3[]; } {
    if (this._cachedFrames && this._lastPhase === this.phase) {
      return this._cachedFrames;
    }
    this._lastPhase = this.phase;
    this._cachedFrames = super.computeFrenetFrames(segments, closed);
    return this._cachedFrames;
  }
}

// Pre-allocate reusable vectors for performance
const tempVector = new THREE.Vector3();
const tempNormal = new THREE.Vector3();
const tempVertex = new THREE.Vector3();

// --- Optimized helper function to update tube vertices ---
const updateTubeVertices = (
  mesh: THREE.Mesh | null,
  curve: SineWaveCurve | null,
  radius: number,
  tubularSegments: number,
  radialSegments: number
) => {
  if (!mesh || !curve || !(mesh.geometry instanceof THREE.BufferGeometry)) return;

  const geometry = mesh.geometry;
  const positionAttribute = geometry.getAttribute('position') as THREE.BufferAttribute;
  const normalAttribute = geometry.getAttribute('normal') as THREE.BufferAttribute;
  
  // Use cached Frenet frames
  const frames = curve.computeFrenetFrames(tubularSegments, false);

  for (let i = 0; i <= tubularSegments; i++) {
    const P = curve.getPointAt(i / tubularSegments, tempVector);
    const N = frames.normals[i];
    const B = frames.binormals[i];

    for (let j = 0; j <= radialSegments; j++) {
      const v = (j / radialSegments) * Math.PI * 2;
      const sin = Math.sin(v);
      const cos = -Math.cos(v);

      // Reuse temp vectors
      tempNormal.set(
        cos * N.x + sin * B.x,
        cos * N.y + sin * B.y,
        cos * N.z + sin * B.z
      ).normalize();

      tempVertex.set(
        P.x + radius * tempNormal.x,
        P.y + radius * tempNormal.y,
        P.z + radius * tempNormal.z
      );

      const index = i * (radialSegments + 1) + j;
      if (index < positionAttribute.count) {
        positionAttribute.setXYZ(index, tempVertex.x, tempVertex.y, tempVertex.z);
        normalAttribute.setXYZ(index, tempNormal.x, tempNormal.y, tempNormal.z);
      }
    }
  }

  positionAttribute.needsUpdate = true;
  normalAttribute.needsUpdate = true;
  geometry.computeBoundingSphere();
};

export const Network3dModel = ({
  isConnected = true,
  isInternetReachable = true,
  networkType = "wifi",
  signalStrength = 1,
  latencyMs = null,
  startDelay = DEFAULT_START_DELAY,
}: Network3dModelProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const groupRef = useRef<THREE.Group>(null!);
  const downloadPointsRefs = useRef<(THREE.Points | null)[]>([]);
  const uploadPointsRefs = useRef<(THREE.Points | null)[]>([]);
  
  // Use refs instead of state for animation values
  const phaseRef = useRef(0);
  const downloadCurveRef = useRef<SineWaveCurve | null>(null);
  const uploadCurveRef = useRef<SineWaveCurve | null>(null);
  const latencyBaseCurveRef = useRef<SineWaveCurve | null>(null);

  // Refs for tube MESHES
  const downloadTubeMeshesRef = useRef<(THREE.Mesh | null)[]>([]);
  const uploadTubeMeshesRef = useRef<(THREE.Mesh | null)[]>([]);

  const particleProgress = useRef<{ download: number[][]; upload: number[][] }>(
    { download: [], upload: [] }
  );

  // Add a ref to track if animation should start yet
  const startTimeRef = useRef<number | null>(null);
  const canAnimateRef = useRef<boolean>(false);

  // Stable reference for latency points to avoid re-renders
  const latencyPointsRef = useRef<THREE.Vector3[]>([]);
  const [latencyPointsVersion, setLatencyPointsVersion] = useState(0);

  // Initialize curves and initial latency points
  useEffect(() => {
    const scale = 2.5;
    const amplitude = 0.4;
    const frequency = 1.7;

    downloadCurveRef.current = new SineWaveCurve(scale, amplitude, frequency, 0, 0);
    uploadCurveRef.current = new SineWaveCurve(scale, amplitude, frequency, Math.PI / 2, 0);
    latencyBaseCurveRef.current = new SineWaveCurve(scale, amplitude * 0.2, frequency * 0.7, Math.PI / 4, 0);

    if (latencyBaseCurveRef.current) {
      const initialPoints = latencyBaseCurveRef.current.getPoints(64).map((p) => p.clone());
      latencyPointsRef.current = initialPoints;
      setLatencyPointsVersion(prev => prev + 1);
    }

    startTimeRef.current = Date.now();
    const timer = setTimeout(() => setIsLoading(false), startDelay);

    return () => {
      clearTimeout(timer);
      canAnimateRef.current = false;
      startTimeRef.current = null;
    };
  }, [startDelay]);

  // Initialize particle progress
  useEffect(() => {
    if (particleProgress.current.download.length === 0) {
      particleProgress.current.download = Array(NUM_TUBE_LAYERS).fill(0).map(() => 
        Array(MAX_PARTICLES_PER_TUBE).fill(0).map(() => Math.random())
      );
    }
    if (particleProgress.current.upload.length === 0) {
      particleProgress.current.upload = Array(NUM_TUBE_LAYERS).fill(0).map(() => 
        Array(MAX_PARTICLES_PER_TUBE).fill(0).map(() => Math.random())
      );
    }
    downloadPointsRefs.current = downloadPointsRefs.current.slice(0, NUM_TUBE_LAYERS);
    uploadPointsRefs.current = uploadPointsRefs.current.slice(0, NUM_TUBE_LAYERS);
  }, []);

  // Memoized materials - stable references
  const downloadMaterial = useMemo(() => 
    new THREE.MeshStandardMaterial({ 
      color: DOWNLOAD_COLOR, 
      emissive: DOWNLOAD_COLOR, 
      emissiveIntensity: 1, 
      wireframe: true, 
      transparent: true, 
      opacity: 0.8 
    }), []
  );
  
  const uploadMaterial = useMemo(() => 
    new THREE.MeshStandardMaterial({ 
      color: UPLOAD_COLOR, 
      emissive: UPLOAD_COLOR, 
      emissiveIntensity: 1, 
      wireframe: true, 
      transparent: true, 
      opacity: 0.8 
    }), []
  );

  // Memoized tube materials for each layer to avoid shared material issues
  const downloadTubeMaterials = useMemo(() => 
    Array(NUM_TUBE_LAYERS).fill(0).map(() => 
      new THREE.MeshStandardMaterial({ 
        color: DOWNLOAD_COLOR, 
        emissive: DOWNLOAD_COLOR, 
        emissiveIntensity: 1, 
        wireframe: true, 
        transparent: true, 
        opacity: 0.8 
      })
    ), []
  );
  
  const uploadTubeMaterials = useMemo(() => 
    Array(NUM_TUBE_LAYERS).fill(0).map(() => 
      new THREE.MeshStandardMaterial({ 
        color: UPLOAD_COLOR, 
        emissive: UPLOAD_COLOR, 
        emissiveIntensity: 1, 
        wireframe: true, 
        transparent: true, 
        opacity: 0.8 
      })
    ), []
  );

  // Create initial tube geometries once using useMemo
  const initialTubeGeometries = useMemo(() => {
    if (__DEV__) { console.log("[Network3D] Creating initial tube geometries..."); }
    const dlGeoms: (THREE.BufferGeometry | null)[] = [];
    const ulGeoms: (THREE.BufferGeometry | null)[] = [];
    
    const dlCurve = new SineWaveCurve(2.5, 0.4, 1.7, 0, 0);
    const ulCurve = new SineWaveCurve(2.5, 0.4, 1.7, Math.PI / 2, 0);

    for (let i = 0; i < NUM_TUBE_LAYERS; i++) {
      const radius = TUBE_RADII[Math.min(i, TUBE_RADII.length - 1)] * 2;
      dlGeoms.push(new THREE.TubeGeometry(dlCurve, TUBE_SEGMENTS, radius, TUBE_RADIAL_SEGMENTS, false));
      ulGeoms.push(new THREE.TubeGeometry(ulCurve, TUBE_SEGMENTS, radius, TUBE_RADIAL_SEGMENTS, false));
    }
    return { dlGeoms, ulGeoms };
  }, []);

  // Memoized particle positions to avoid recreation
  const particlePositionsArrays = useMemo(() => 
    Array(NUM_TUBE_LAYERS * 2).fill(0).map(() => new Float32Array(MAX_PARTICLES_PER_TUBE * 3)),
    []
  );

  // Throttled latency update to reduce frequency
  const lastLatencyUpdateRef = useRef(0);
  const LATENCY_UPDATE_INTERVAL = 16; // ~60fps

  // Animation frame loop
  useFrame((state, delta) => {
    // Check if animation delay has passed
    if (startTimeRef.current && !canAnimateRef.current) {
      const elapsed = Date.now() - startTimeRef.current;
      if (elapsed >= startDelay) {
        canAnimateRef.current = true;
      }
    }

    // Only animate if delay has passed and component is allowed to animate
    if (canAnimateRef.current && isConnected && downloadCurveRef.current && uploadCurveRef.current && latencyBaseCurveRef.current) {
      phaseRef.current += delta * BASE_WAVE_SPEED;
      downloadCurveRef.current.phase = phaseRef.current;
      uploadCurveRef.current.phase = phaseRef.current + Math.PI / 2;
      latencyBaseCurveRef.current.phase = phaseRef.current + Math.PI / 4;

      // Update Tube Vertices
      downloadTubeMeshesRef.current.forEach((mesh, index) => {
        const radius = TUBE_RADII[Math.min(index, TUBE_RADII.length - 1)] * 2;
        updateTubeVertices(mesh, downloadCurveRef.current, radius, TUBE_SEGMENTS, TUBE_RADIAL_SEGMENTS);
      });
      uploadTubeMeshesRef.current.forEach((mesh, index) => {
        const radius = TUBE_RADII[Math.min(index, TUBE_RADII.length - 1)] * 2;
        updateTubeVertices(mesh, uploadCurveRef.current, radius, TUBE_SEGMENTS, TUBE_RADIAL_SEGMENTS);
      });
    }

    // Update Tube Materials
    let targetOpacity = 0.8;
    let targetIntensity = 1.0;
    if (isConnected === false) {
      targetOpacity = 0.15; 
      targetIntensity = 0.5;
    } else if (isInternetReachable === false) {
      targetOpacity = 0.6; 
      targetIntensity = 0.8;
    } else {
      targetIntensity = 0.5 + (signalStrength ?? 1) * 0.5;
      targetOpacity = 0.6 + (signalStrength ?? 1) * 0.2;
    }

    // Update individual tube materials with layer-based opacity
    downloadTubeMaterials.forEach((material, index) => {
      const layerOpacity = targetOpacity * (1 - index * 0.25);
      material.opacity = THREE.MathUtils.lerp(material.opacity, layerOpacity, delta * 5);
      material.emissiveIntensity = THREE.MathUtils.lerp(material.emissiveIntensity, targetIntensity, delta * 5);
    });
    
    uploadTubeMaterials.forEach((material, index) => {
      const layerOpacity = targetOpacity * (1 - index * 0.25);
      material.opacity = THREE.MathUtils.lerp(material.opacity, layerOpacity, delta * 5);
      material.emissiveIntensity = THREE.MathUtils.lerp(material.emissiveIntensity, targetIntensity, delta * 5);
    });

    // Particle Animation
    if (canAnimateRef.current) {
      const currentDownloadSpeed = BASE_PARTICLE_SPEED * (isConnected ? 1 : 0);
      const currentUploadSpeed = BASE_PARTICLE_SPEED * (isConnected ? 1 : 0);

      for (let layer = 0; layer < NUM_TUBE_LAYERS; layer++) {
        const downloadPositions = downloadPointsRefs.current[layer]?.geometry.attributes.position;
        const uploadPositions = uploadPointsRefs.current[layer]?.geometry.attributes.position;
        const yOffset = layer * TUBE_Y_OFFSET;

        if (downloadPositions && downloadCurveRef.current) {
          for (let i = 0; i < MAX_PARTICLES_PER_TUBE; i++) {
            particleProgress.current.download[layer][i] = (particleProgress.current.download[layer][i] + delta * currentDownloadSpeed) % 1;
            const point = downloadCurveRef.current.getPointAt(particleProgress.current.download[layer][i]);
            downloadPositions.setXYZ(i, point.x, point.y + yOffset, point.z);
          }
          downloadPositions.needsUpdate = true;
        }
        if (uploadPositions && uploadCurveRef.current) {
          for (let i = 0; i < MAX_PARTICLES_PER_TUBE; i++) {
            particleProgress.current.upload[layer][i] -= delta * currentUploadSpeed;
            if (particleProgress.current.upload[layer][i] < 0) particleProgress.current.upload[layer][i] += 1;
            const point = uploadCurveRef.current.getPointAt(particleProgress.current.upload[layer][i]);
            uploadPositions.setXYZ(i, point.x, point.y + yOffset, point.z);
          }
          uploadPositions.needsUpdate = true;
        }
      }
    }

    // Throttled Latency Line Jitter Animation
    const now = performance.now();
    if (canAnimateRef.current && latencyBaseCurveRef.current && (now - lastLatencyUpdateRef.current) > LATENCY_UPDATE_INTERVAL) {
      lastLatencyUpdateRef.current = now;
      const jitterFactor = latencyMs === null ? 0 : Math.min(1, latencyMs / LATENCY_MAX_THRESHOLD);
      const time = state.clock.getElapsedTime();
      
      // Reuse existing points array to avoid allocation
      latencyBaseCurveRef.current.getPoints(64).forEach((p, index) => {
        const jitterOffset = Math.sin(time * 5 + index * 0.5) * LATENCY_JITTER_AMOUNT * jitterFactor;
        if (latencyPointsRef.current[index]) {
          latencyPointsRef.current[index].copy(p).setY(p.y + jitterOffset);
        }
      });
      setLatencyPointsVersion(prev => prev + 1);
    }
  });

  // Memoized grid colors to avoid recreation
  const gridColors = useMemo(() => ({
    cellColor: new THREE.Color(DOWNLOAD_COLOR),
    sectionColor: new THREE.Color(UPLOAD_COLOR)
  }), []);

  return (
    <group ref={groupRef} rotation={[0, 0, 0]}>
      {initialTubeGeometries.dlGeoms.map((geometry, index) => (
        geometry && (
          <group key={`dl-layer-${index}`} position={[0, index * TUBE_Y_OFFSET, 0]}>
            <mesh
              ref={(el) => (downloadTubeMeshesRef.current[index] = el)}
              geometry={geometry}
              material={downloadTubeMaterials[index]}
              position={[index * 0.3, 0, 0]}
            />
            <Points
              position={[index * 0.3, 0, 0]}
              ref={(el) => (downloadPointsRefs.current[index] = el)}
              positions={particlePositionsArrays[index]}
              stride={3}
              frustumCulled={false}
            >
              <PointMaterial 
                color={DOWNLOAD_COLOR} 
                size={0.08 - index * 0.005} 
                sizeAttenuation={true} 
                depthWrite={false} 
                blending={THREE.AdditiveBlending} 
              />
            </Points>
          </group>
        )
      ))}

      {initialTubeGeometries.ulGeoms.map((geometry, index) => (
        geometry && (
          <group key={`ul-layer-${index}`} position={[0, index * TUBE_Y_OFFSET, 0]}>
            <mesh
              ref={(el) => (uploadTubeMeshesRef.current[index] = el)}
              geometry={geometry}
              material={uploadTubeMaterials[index]}
              position={[index * 0.3, 0, 0]}
            />
            <Points
              position={[index * 0.3, 0, 0]}
              ref={(el) => (uploadPointsRefs.current[index] = el)}
              positions={particlePositionsArrays[index + NUM_TUBE_LAYERS]}
              stride={3}
              frustumCulled={false}
            >
              <PointMaterial 
                color={UPLOAD_COLOR} 
                size={0.08 - index * 0.005} 
                sizeAttenuation={true} 
                depthWrite={false} 
                blending={THREE.AdditiveBlending} 
              />
            </Points>
          </group>
        )
      ))}

      {latencyPointsRef.current.length > 1 && (
        <DreiLine 
          key={latencyPointsVersion} // Force re-render when points change
          points={latencyPointsRef.current} 
          color={LATENCY_COLOR} 
          lineWidth={2} 
          dashed={true} 
          dashScale={10} 
          gapSize={0.5} 
          transparent={true} 
          opacity={0.6} 
        />
      )}

      <Grid 
        position={[0, 0, -0.5]} 
        args={[10, 10]} 
        cellSize={0.5} 
        cellThickness={1} 
        cellColor={gridColors.cellColor} 
        sectionSize={2} 
        sectionThickness={1.5} 
        sectionColor={gridColors.sectionColor} 
        fadeDistance={8} 
        fadeStrength={1} 
        infiniteGrid={true} 
        followCamera={false} 
        rotation={[Math.PI / 2, 0, 0]} 
      />
    </group>
  );
};