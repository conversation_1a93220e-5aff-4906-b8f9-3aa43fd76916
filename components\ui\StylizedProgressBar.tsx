import React from 'react';
import { View } from 'react-native';
import Svg, { Rect, Defs, LinearGradient, Stop } from 'react-native-svg';

interface StylizedProgressBarProps {
  percentage: number | undefined;
  barHeight?: number;
  bgColor?: string; // Background color of the track (optional)
  progressColor?: string; // Main color of the progress fill
  gradientColors?: [string, string]; // Optional gradient for the fill
  alertThreshold?: number; // Percentage threshold to turn red
  alertColor?: string; // Color when threshold is met
}

export const StylizedProgressBar = ({
  percentage,
  barHeight = 6, // Slightly thicker than before
  bgColor = "rgba(0, 191, 255, 0.1)", // Faint cyan background
  progressColor = "#00BFFF", // Default cyan
  gradientColors, // e.g., ['#00BFFF', '#00FFFF']
  alertThreshold,
  alertColor = "#FF0000", // Default red
}: StylizedProgressBarProps) => {
  const validPercentage = (percentage && !isNaN(percentage)) ? Math.max(0, Math.min(1, percentage)) : 0;
  const widthPercentage = validPercentage * 100;

  let currentFillColor = progressColor;
  let useGradient = !!gradientColors;

  // Check alert threshold
  if (alertThreshold !== undefined && validPercentage >= alertThreshold) {
    currentFillColor = alertColor;
    useGradient = false; // Don't use gradient when alerting
  }

  return (
    <View style={{ height: barHeight, width: '100%', marginVertical: 4 }}>
      <Svg height={barHeight} width="100%">
        <Defs>
          {useGradient && gradientColors && (
            <LinearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="0%">
              <Stop offset="0%" stopColor={gradientColors[0]} stopOpacity="1" />
              <Stop offset="100%" stopColor={gradientColors[1]} stopOpacity="1" />
            </LinearGradient>
          )}
        </Defs>
        {bgColor && (
           <Rect x="0" y="0" width="100%" height={barHeight} fill={bgColor} rx={barHeight / 2} ry={barHeight / 2} />
        )}
        <Rect
          x="0"
          y="0"
          width={`${widthPercentage}%`}
          height={barHeight}
          fill={useGradient ? "url(#grad)" : currentFillColor}
          rx={barHeight / 2} 
          ry={barHeight / 2}
        />
      </Svg>
    </View>
  );
};
