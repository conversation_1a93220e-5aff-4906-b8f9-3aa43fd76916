import React, { useEffect, useRef } from 'react';
import { View, Animated, Easing, Dimensions, StyleSheet } from 'react-native'; 

const screenHeight = Dimensions.get('window').height;
const sliceHeight = 20; 
const numSlices = Math.ceil(screenHeight / sliceHeight);

interface GlitchOverlayProps {
  isGlitching: boolean;
}

const GlitchSlice = ({ delay }: { delay: number }) => {
  const glitchAnim = useRef(new Animated.Value(0)).current;
  const intensity = (Math.random() * 10) + 2; 

  useEffect(() => {
    const animation = Animated.sequence([
      Animated.delay(delay), 
      Animated.timing(glitchAnim, { toValue: 1, duration: 50, easing: Easing.linear, useNativeDriver: true }),
      Animated.timing(glitchAnim, { toValue: 0, duration: 50, easing: Easing.linear, useNativeDriver: true }),
    ]);

    
    Animated.loop(animation, { iterations: 2 }).start();

  }, [glitchAnim, delay]);

  const translateX = glitchAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, (Math.random() > 0.5 ? 1 : -1) * intensity], 
  });

  return (
    <Animated.View
      
      className="h-[20px] w-full bg-aegis-red/10" 
      style={{ transform: [{ translateX }] }}
    />
  );
};



export const GlitchOverlay = ({ isGlitching }: GlitchOverlayProps) => {
  if (!isGlitching) {
    return null;
  }

  return (
    <View style={StyleSheet.absoluteFill} pointerEvents="none">
      {Array.from({ length: numSlices }).map((_, i) => (
        <GlitchSlice key={i} delay={Math.random() * 50} />
      ))}
    </View>
  );
};


