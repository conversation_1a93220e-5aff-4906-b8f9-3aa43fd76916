import React, { useState, useEffect, useCallback } from "react";
import { View, Text, ScrollView, Alert, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { useSystemInfoStore } from "../store/systemInfoStore";
import { StatSection } from "../components/sections/StatSection";
import { StatLine } from "../components/ui/StatLine";
import { StylizedProgressBar } from "../components/ui/StylizedProgressBar";
import { StorageIcon } from "../components/icons/StorageIcon";
import { formatBytes } from "../lib/utils";
import { ScanlineOverlay } from "../components/layout/ScanlineOverlay";
import { GlitchOverlay } from "../components/layout/GlitchOverlay";
import { ScrollingLogSection } from "../components/sections/ScrollingLogSection";
import { Storage3dModel } from "../components/core/Storage3dModel";
import { Canvas } from "@react-three/fiber/native";
import { Suspense } from "react";

export default function StorageDetailScreen() {
  const {
    totalStorage,
    freeStorage,
    osName,
    osVersion,
    deviceYearClass,
    storageLogMessages,
    addStorageLogMessage,
  } = useSystemInfoStore();
  const storageUsedPercentage =
    totalStorage && freeStorage && totalStorage > 0
      ? (totalStorage - freeStorage) / totalStorage
      : 0;

  const isCriticallyFull = storageUsedPercentage >= 0.95;
  const isVeryHigh = storageUsedPercentage >= 0.90;
  const isHigh = storageUsedPercentage >= 0.75;

  let usageLevelValue = "NOMINAL";
  let usageLevelColor = "text-aegis-cyan";
  let usageLevelGlow: 'cyan' | 'orange' | 'red' | undefined = 'cyan';

  if (isCriticallyFull) {
    usageLevelValue = "CRITICAL";
    usageLevelColor = "text-aegis-red";
    usageLevelGlow = 'red';
  } else if (isVeryHigh) {
    usageLevelValue = "VERY HIGH";
    usageLevelColor = "text-aegis-orange";
    usageLevelGlow = 'orange';
  } else if (isHigh) {
    usageLevelValue = "HIGH";
    usageLevelColor = "text-yellow-500";
    usageLevelGlow = 'orange';
  }

  const [lastScanTime, setLastScanTime] = useState(new Date());
  const [isScanning, setIsScanning] = useState(false);

  const triggerThematicScan = useCallback(() => {
    setIsScanning(true);
    setTimeout(() => {
      setLastScanTime(new Date());
      setIsScanning(false);
      addStorageLogMessage("> Integrity Check: System Nominal (Thematic)");
    }, 1500 + Math.random() * 1000);
  }, [addStorageLogMessage]);

  useEffect(() => {
    triggerThematicScan();
  }, [triggerThematicScan]);

  const formattedLastScanTime = isScanning
    ? "ANALYZING..."
    : `${lastScanTime.getHours().toString().padStart(2, '0')}:${lastScanTime.getMinutes().toString().padStart(2, '0')}:${lastScanTime.getSeconds().toString().padStart(2, '0')}`;

  const [isGlitching, setIsGlitching] = useState(false);

  useEffect(() => {
    setIsGlitching(true);
    const timer = setTimeout(() => setIsGlitching(false), 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-black">
      <Stack.Screen
        options={{
          headerShown: true,
          headerStyle: { backgroundColor: "#000000" },
          headerTintColor: "#00BFFF",
          headerTitleStyle: { fontFamily: "Orbitron-Bold", color: "#FF6600" },
          title: "STORAGE DETAIL",
          headerBackTitle: "Back",
        }}
      />
      <ScanlineOverlay />
      <GlitchOverlay isGlitching={isGlitching} />
      <View className="flex-1 p-3">
        <View className="flex-[0.6] mb-3 border border-aegis-cyan/20">
          <Suspense fallback={null}>
            <Canvas
              camera={{ position: [0, 0, 3.5], fov: 50 }}
              gl={__DEV__ ? { debug: { checkShaderErrors: false, onShaderError: null } } : undefined}
              onCreated={({ gl }) => {
                // FIXED: Add GL context error handling
                gl.domElement.addEventListener('webglcontextlost', () => {
                  console.warn('Storage Canvas: WebGL context lost');
                });
                gl.domElement.addEventListener('webglcontextrestored', () => {
                  console.warn('Storage Canvas: WebGL context restored');
                });
              }}
            >
              <ambientLight intensity={0.5} />
              <directionalLight position={[2, 3, 5]} intensity={0.8} />
              <Storage3dModel storageUsage={storageUsedPercentage} />
            </Canvas>
          </Suspense>
        </View>
        <View className="">
          <ScrollingLogSection
            title="CRITICAL FILE STORAGE LOG"
            lineHeight={20}
            logMessages={storageLogMessages}
          />
        </View>
        <ScrollView className="flex-[0.4]">
          <StatSection title="DATA STORAGE">
            <StatLine
              label="Usage"
              value={
                storageUsedPercentage
                  ? `${(storageUsedPercentage * 100).toFixed(1)} %`
                  : "-- %"
              }
              icon={<StorageIcon />}
              valueGlow={usageLevelGlow}
              valueColor={usageLevelColor}
            />
            <StylizedProgressBar
              percentage={storageUsedPercentage}
              alertThreshold={0.9}
            />
            <StatLine
              label="Used"
              value={formatBytes(
                typeof totalStorage === "number" &&
                  typeof freeStorage === "number"
                  ? totalStorage - freeStorage
                  : undefined
              )}
              valueGlow="cyan"
            />
            <StatLine
              label="Free"
              value={formatBytes(freeStorage)}
              valueGlow={usageLevelGlow}
              valueColor={usageLevelColor}
            />
            <StatLine
              label="Total"
              value={formatBytes(totalStorage)}
              valueGlow="cyan"
            />
            <View className="mt-2 border-t border-aegis-cyan/20 pt-1">
              <StatLine label="USAGE LEVEL" value={usageLevelValue} valueColor={usageLevelColor} valueGlow={usageLevelGlow} />
              <View className="flex-row items-center">
                <StatLine
                  label="LAST INTEGRITY CHECK"
                  value={formattedLastScanTime}
                  valueColor={isScanning ? "text-yellow-500" : "text-aegis-cyan"}
                />
                <TouchableOpacity
                  onPress={() =>
                    Alert.alert(
                      "Integrity Check (Thematic)",
                      "This represents a simulated system integrity check for thematic purposes. No actual deep storage scan is performed by this application."
                    )
                  }
                  className="ml-2 p-1 rounded-full bg-aegis-cyan/20"
                >
                  <Text className="text-aegis-cyan text-xs font-bold">i</Text>
                </TouchableOpacity>
              </View>
              <StatLine label="PRIMARY VOLUME ID" value="[ SYS_VOL_01 ]" />
            </View>
            <StatLine label="OS" value={`${osName ?? ""} ${osVersion ?? ""}`} />
            <StatLine
              label="Device Class"
              value={deviceYearClass?.toString() ?? "N/A"}
            />
          </StatSection>

          <StatSection title="STORAGE ALLOCATION (SIMULATED)">
            <TouchableOpacity
              onPress={() =>
                Alert.alert(
                  "Simulated Allocation",
                  "This is an illustrative breakdown based on general device usage estimates and common file types. It does not represent an exact file-by-file analysis of your storage."
                )
              }
              className="absolute top-3 right-3 p-1 rounded-full bg-aegis-cyan/20 z-10"
            >
              <Text className="text-aegis-cyan text-xs font-bold">i</Text>
            </TouchableOpacity>
            {(() => {
              const usage = storageUsedPercentage ?? 0;
              const total = totalStorage ?? 0;
              const systemProp = 0.15;
              const appsProp = 0.4 * usage;
              const mediaProp = 0.35 * usage;
              const cacheProp = 0.1 * usage;
              const totalUsedProp = appsProp + mediaProp + cacheProp;
              const scaleFactor =
                totalUsedProp > usage ? usage / totalUsedProp : 1;

              const systemSize = total * systemProp;
              const appsSize = total * appsProp * scaleFactor;
              const mediaSize = total * mediaProp * scaleFactor;
              const cacheSize = total * cacheProp * scaleFactor;
              const freeSize = freeStorage ?? 0;

              const simulatedUsed =
                systemSize + appsSize + mediaSize + cacheSize;

              return (
                <>
                  <StatLine
                    label="System"
                    value={formatBytes(systemSize)}
                    valueColor="text-red-500"
                  />
                  <StatLine
                    label="Applications"
                    value={formatBytes(appsSize)}
                    valueColor="text-yellow-500"
                  />
                  <StatLine
                    label="Media"
                    value={formatBytes(mediaSize)}
                    valueColor="text-purple-500"
                  />
                  <StatLine
                    label="Cache/Other"
                    value={formatBytes(cacheSize)}
                    valueColor="text-gray-500"
                  />
                  <StatLine
                    label="Free"
                    value={formatBytes(freeSize)}
                    valueColor="text-aegis-cyan"
                    valueGlow="cyan"
                  />
                </>
              );
            })()}
          </StatSection>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
