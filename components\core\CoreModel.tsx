import React, { useRef, useMemo, useEffect, useState } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber/native';
import * as THREE from 'three';
import { Text } from '@react-three/drei/native'; // Import Text for rendering

// Abstracted Core component inspired by biomechanical aesthetics, featuring defensive fields, bio-fluid, and anomalous patterns
export function CoreModel({
  powerLevel = 0.8, // Renamed from batteryLevel
  powerStatus = 'DISCHARGING', // Renamed from batteryStatus
  isConnected = true,
  syncRate = 1.0, // Renamed from syncRatio (e.g., Unit synchronization rate)
  overloadState = false, // Renamed from berserkerMode (e.g., Unit overload state)
  damageLevel = 0, // Structural integrity (0-1)
  operatorVitals = 1.0, // Renamed from pilotVitals (Operator life signs)
  unitId = 'TestType', // Renamed from evaUnit (Abstract Unit identifier: Proto, TestType, ProdModel, etc.)
}: {
  powerLevel?: number,
  powerStatus: string,
  isConnected: boolean | null,
  syncRate?: number,
  overloadState?: boolean,
  damageLevel?: number,
  operatorVitals?: number,
  unitId?: string,
}) {
  const { clock } = useThree();
  const groupRef = useRef<THREE.Group>(null!);
  const meshRef = useRef<THREE.Mesh>(null!);
  const innerMeshRef = useRef<THREE.Mesh>(null!);
  const shellRef = useRef<THREE.Mesh>(null!);
  const barrierRef = useRef<THREE.Group>(null!); // Renamed from atFieldRef
  const hexGridRef = useRef<THREE.LineSegments>(null!);
  const dataRingsRef = useRef<THREE.Group>(null!);
  const bioFluidRef = useRef<THREE.Mesh>(null!); // Renamed from lclFluidRef
  const terminalPatternRef = useRef<THREE.Mesh>(null!);
  const anomalyPatternRef = useRef<THREE.Points>(null!); // Renamed from angelPatternRef
  const damageOverlayRef = useRef<THREE.Mesh>(null!);
  const organizationLogoRef = useRef<THREE.Group>(null!); // Renamed from nervLogoRef
  const glitchTimerRef = useRef(0);
  const [glitchIntensity, setGlitchIntensity] = useState(0);
  const timeRef = useRef(0);
  const soundWaveRef = useRef<THREE.Mesh>(null!);

  // Abstracted color scheme with unit type variations
  const colors = useMemo(() => {
    // Base operational colors
    const baseColors = {
      critical: '#FF0000', // Critical red
      warning: '#FF6600', // Warning orange
      caution: '#FFFF00', // Caution yellow
      normal: '#00BFFF', // Standard blue
      active: '#FF00FF', // Active magenta
      barrier: '#FFA500', // Defensive Barrier orange
      hexGrid: '#00FFFF', // Cyan for hex grid
      dataRing1: '#7B68EE', // Medium slate blue
      dataRing2: '#32CD32', // Lime green
      dataRing3: '#FF1493', // Deep pink
      overload: '#8B0000', // Dark red for overload state
      bioFluid: '#FFA07A', // Light salmon for Bio-Fluid
      organization: '#FFFFFF', // White for Organization logo
      anomaly: '#F0E68C', // Khaki for Anomaly pattern
    };

    // Unit type-specific colors (abstracted)
    const unitTypeColors = {
      Proto: '#0066CC', // Prototype - blue
      TestType: '#9400D3', // Test type - purple
      ProdModel: '#FF0000', // Production model - red
      Stealth: '#228B22', // Stealth type - green
      Heavy: '#FFD700', // Heavy type - gold
      Advanced: '#4682B4', // Advanced type - steel blue
      Omega: '#000000', // Omega type - black
    };

    // Set primary unit color based on unit ID
    let unitColor = unitTypeColors[unitId as keyof typeof unitTypeColors] || baseColors.active;

    return {
      ...baseColors,
      unit: unitColor,
      // Use unit color for active elements if available
      active: unitColor || baseColors.active,
    };
  }, [unitId]);

  // Set primary color based on power level and damage (System status indicator)
  const primaryColor = useMemo(() => {
    // If in overload state, override with overload color
    if (overloadState) return colors.overload;

    // If damaged, blend with critical color
    if (damageLevel > 0.5) {
      return blendColors(
        powerLevel < 0.2 ? colors.critical :
        powerLevel < 0.5 ? colors.warning :
        powerLevel < 0.8 ? colors.caution : colors.unit,
        colors.critical,
        damageLevel - 0.5
      );
    }

    if (powerLevel < 0.2) return colors.critical;
    if (powerLevel < 0.5) return colors.warning;
    if (powerLevel < 0.8) return colors.caution;
    return colors.unit;
  }, [powerLevel, colors, overloadState, damageLevel]);

  // Determine Barrier color and intensity based on connection status, sync rate, and overload state
  const barrierColor = useMemo(() => {
    if (!isConnected) return '#555555'; // Dim if disconnected
    if (overloadState) return colors.overload; // Overload color if active
    // Blend barrier base color with unit color based on sync rate
    return blendColors(colors.barrier, colors.unit, syncRate * 0.5);
  }, [isConnected, colors, syncRate, overloadState]);

  // Helper function to blend colors (no changes needed here)
  function blendColors(color1: string, color2: string, ratio: number) {
    // Convert hex to RGB
    const c1 = new THREE.Color(color1);
    const c2 = new THREE.Color(color2);
    
    // Blend
    const blended = new THREE.Color(
      c1.r * (1 - ratio) + c2.r * ratio,
      c1.g * (1 - ratio) + c2.g * ratio,
      c1.b * (1 - ratio) + c2.b * ratio
    );
    
    return '#' + blended.getHexString();
  }

  // Calculate glitch effect based on power status, sync rate, and overload state
  useEffect(() => {
    // Base glitch on error status or low sync rate
    let newGlitchIntensity = powerStatus === 'ERROR'
      ? 0.3
      : (1 - (syncRate || 1)) * 0.2; // Higher glitch for lower sync

    // Increase glitch in overload state
    if (overloadState) {
      newGlitchIntensity = Math.max(newGlitchIntensity, 0.4);
    }

    // Add damage-based glitching
    newGlitchIntensity += damageLevel * 0.3;

    // Cap at reasonable maximum
    setGlitchIntensity(Math.min(newGlitchIntensity, 0.8));
  }, [powerStatus, syncRate, overloadState, damageLevel]);

  // Create hexagonal grid pattern (like Organization terminal displays)
  const hexGridGeometry = useMemo(() => {
    const geo = new THREE.BufferGeometry();
    const vertices = [];
    const radius = 1.6;
    const segments = 12;
    const rings = 8;
    
    // Create hexagonal grid on a sphere
    for (let ring = 0; ring < rings; ring++) {
      const phi = (Math.PI * ring) / rings;
      const ringRadius = Math.sin(phi) * radius;
      const y = Math.cos(phi) * radius;
      
      for (let segment = 0; segment < segments; segment++) {
        const theta = (segment / segments) * Math.PI * 2;
        const x = Math.cos(theta) * ringRadius;
        const z = Math.sin(theta) * ringRadius;
        
        // Hexagon vertices
        const hexRadius = 0.1 * (1 - ring / rings);
        for (let i = 0; i < 6; i++) {
          const angle = (i / 6) * Math.PI * 2;
          const nextAngle = ((i + 1) / 6) * Math.PI * 2;
          
          vertices.push(
            x + Math.cos(angle) * hexRadius, 
            y + hexRadius * 0.2, 
            z + Math.sin(angle) * hexRadius
          );
          vertices.push(
            x + Math.cos(nextAngle) * hexRadius, 
            y + hexRadius * 0.2, 
            z + Math.sin(nextAngle) * hexRadius
          );
        }
      }
    }
    
    geo.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    return geo;
  }, []);

  const createBarrier = useMemo(() => {
    const group = new THREE.Group();

    const mainField = new THREE.Mesh(
      new THREE.OctahedronGeometry(1, 2),
      new THREE.MeshBasicMaterial({
        color: barrierColor,
        wireframe: true,
        transparent: true,
        opacity: 0.4,
        side: THREE.DoubleSide,
      })
    );
    group.add(mainField);

    const secondaryField = new THREE.Mesh(
      new THREE.IcosahedronGeometry(1.2, 1),
      new THREE.MeshBasicMaterial({
        color: barrierColor,
        wireframe: true,
        transparent: true,
        opacity: 0.2,
        side: THREE.DoubleSide,
      })
    );
    group.add(secondaryField);

    const fragments = new THREE.Group();

    for (let i = 0; i < 12; i++) {
      const fragment = new THREE.Mesh(
        new THREE.CircleGeometry(0.4, 6),
        new THREE.MeshBasicMaterial({
          color: barrierColor,
          transparent: true,
          opacity: 0.3,
          side: THREE.DoubleSide,
        })
      );

      const phi = Math.acos(-1 + (2 * i) / 12);
      const theta = Math.sqrt(12 * Math.PI) * phi;
      
      fragment.position.setFromSphericalCoords(
        1.5,
        phi,
        theta
      );
      
      fragment.lookAt(0, 0, 0);
      fragment.rotation.z = Math.random() * Math.PI;

      fragments.add(fragment);
    }

    group.add(fragments);
    return group;
  }, [barrierColor]);

  const createTerminalPatternTexture = useMemo(() => {
    const texture = new THREE.DataTexture(
      generateCentralSystemPattern(256, 256),
      256, 256,
      THREE.RGBAFormat
    );
    texture.needsUpdate = true;

    return texture;
  }, []);

  function generateCentralSystemPattern(width: number, height: number) {
    const size = width * height;
    const data = new Uint8Array(4 * size);

    for (let i = 0; i < size; i++) {
      const x = i % width;
      const y = Math.floor(i / width);
      
      const stride = i * 4;

      const isGridLine = x % 32 === 0 || y % 32 === 0;
      const isMainGrid = x % 64 === 0 || y % 64 === 0;

      const isDataBlip = Math.random() > 0.95;

      const distance = Math.sqrt(Math.pow(x - width / 2, 2) + Math.pow(y - height / 2, 2));
      const isInRing = distance < width * 0.4 && distance > width * 0.38;

      if (isMainGrid) {
        data[stride] = 0; data[stride + 1] = 255; data[stride + 2] = 255; data[stride + 3] = 150;
      } else if (isGridLine) {
        data[stride] = 0; data[stride + 1] = 200; data[stride + 2] = 200; data[stride + 3] = 100;
      } else if (isInRing) {
        data[stride] = 255; data[stride + 1] = 50; data[stride + 2] = 50; data[stride + 3] = 200;
      } else if (isDataBlip) {
        data[stride] = 255; data[stride + 1] = 255; data[stride + 2] = 0; data[stride + 3] = 150;
      } else {
        data[stride] = 0; data[stride + 1] = 0; data[stride + 2] = 0; data[stride + 3] = 0;
      }
    }

    return data;
  }

  const createOrganizationLogo = useMemo(() => {
    const group = new THREE.Group();
    const logoScale = 0.2;

    const curve = new THREE.QuadraticBezierCurve3(
      new THREE.Vector3(0, -1, 0),
      new THREE.Vector3(1, 0, 0),
      new THREE.Vector3(0, 1, 0)
    );
    const points = curve.getPoints(10);
    const shape1Geo = new THREE.BufferGeometry().setFromPoints(points);
    const shape1 = new THREE.Line(
      shape1Geo,
      new THREE.LineBasicMaterial({ color: colors.organization })
    );

    const shape2 = shape1.clone();
    shape2.scale.x = -1;

    group.add(shape1);
    group.add(shape2);

    const shape3Geo = new THREE.BufferGeometry().setFromPoints([
      new THREE.Vector3(-0.5, -0.3, 0),
      new THREE.Vector3(0.5, -0.3, 0)
    ]);
    const shape3 = new THREE.Line(
      shape3Geo,
      new THREE.LineBasicMaterial({ color: colors.organization })
    );

    group.add(shape3);
    group.scale.set(logoScale, logoScale, logoScale);

    return group;
  }, [colors]);

  const createDataRings = useMemo(() => {
    const group = new THREE.Group();
    const ringRadius = [1.8, 1.6, 1.4];
    const ringColors = [colors.dataRing1, colors.dataRing2, colors.unit];
    
    ringRadius.forEach((radius, i) => {
      const segments = 128;
      
      const ring = new THREE.Line(
        new THREE.BufferGeometry().setFromPoints(
          Array.from({ length: segments + 1 }, (_, j) => {
            const theta = (j / segments) * Math.PI * 2;
            return new THREE.Vector3(
              Math.cos(theta) * radius,
              0,
              Math.sin(theta) * radius
            );
          })
        ),
        new THREE.LineBasicMaterial({ 
          color: ringColors[i], 
          transparent: true, 
          opacity: 0.8,
        })
      );
      
      ring.rotation.x = Math.PI / 2 + (Math.random() - 0.5) * 0.8;
      ring.rotation.z = (Math.random() - 0.5) * 0.5;
      group.add(ring);
      
      if (i === 0) {
        for (let s = 0; s < 3; s++) {
          const segmentLength = Math.PI * (0.2 + Math.random() * 0.3);
          const startAngle = Math.random() * Math.PI * 2;

          const segment = new THREE.Line(
            new THREE.BufferGeometry().setFromPoints(
              Array.from({ length: Math.floor(segments * 0.2) + 1 }, (_, j) => {
                const theta = startAngle + (j / segments) * segmentLength;
                return new THREE.Vector3(
                  Math.cos(theta) * (radius + 0.05),
                  0,
                  Math.sin(theta) * (radius + 0.05)
                );
              })
            ),
            new THREE.LineBasicMaterial({ 
              color: colors.dataRing3, 
              transparent: true, 
              opacity: 0.9,
            })
          );
          
          segment.rotation.x = Math.PI / 2 + (Math.random() - 0.5) * 0.8;
          segment.rotation.z = (Math.random() - 0.5) * 0.5;
          group.add(segment);
        }
      }
    });
    
    return group;
  }, [colors]);

  const createSoundWave = useMemo(() => {
    const geometry = new THREE.PlaneGeometry(2, 0.2, 50, 1);
    const positions = geometry.attributes.position.array;

    for (let i = 0; i < positions.length; i += 3) {
      positions[i + 1] = Math.sin(positions[i] * 5) * 0.05;
    }
    geometry.computeVertexNormals();

    const material = new THREE.MeshBasicMaterial({
      color: colors.unit,
      wireframe: true,
      transparent: true,
      opacity: 0.7,
    });

    return new THREE.Mesh(geometry, material);
  }, [colors]);

  useEffect(() => {
    // Add Barrier group
    if (barrierRef.current) {
      barrierRef.current.add(createBarrier);
    }

    // Add data rings
    if (dataRingsRef.current) {
      dataRingsRef.current.add(createDataRings);
    }

    // Add Organization logo
    if (organizationLogoRef.current) {
      organizationLogoRef.current.add(createOrganizationLogo);
    }
    // Add sound wave
    if (soundWaveRef.current) {
    }

  }, [createBarrier, createDataRings, createOrganizationLogo]);

  useFrame((state, delta) => {
    if (!groupRef.current) return;

    const time = clock.getElapsedTime();
    timeRef.current = time;

    let rotationSpeed = 0.25;
    if (overloadState) {
      rotationSpeed = 0.8;
    } else if (powerStatus === 'CHARGING') {
      rotationSpeed = 0.5;
    } else if (powerStatus === 'FULL') {
      rotationSpeed = 0.1;
    }

    if (meshRef.current) {
      meshRef.current.rotation.y += delta * rotationSpeed;
      meshRef.current.rotation.x += delta * rotationSpeed * 0.3;

      if (overloadState) {
        const intensity = (Math.sin(time * 8) * 0.5 + 0.5) * 2;
        if (meshRef.current.material instanceof THREE.MeshStandardMaterial) {
          meshRef.current.material.emissiveIntensity = intensity;
        }
      } else {
         if (meshRef.current.material instanceof THREE.MeshStandardMaterial) {
          meshRef.current.material.emissiveIntensity = 0.5;
        }
      }
    }

    if (innerMeshRef.current) {
      innerMeshRef.current.rotation.y -= delta * rotationSpeed * 0.7;
      innerMeshRef.current.rotation.z += delta * rotationSpeed * 0.5;

      const pulseFrequency = overloadState ? 6 : 3;
      const pulseIntensity = overloadState ? 0.2 : 0.1;
      const pulse = Math.sin(time * pulseFrequency) * pulseIntensity + 0.9;
      innerMeshRef.current.scale.set(pulse, pulse, pulse);

      if (overloadState && innerMeshRef.current.material instanceof THREE.MeshBasicMaterial) {
        const blinkIntensity = (Math.sin(time * 15) * 0.5 + 0.5);
        innerMeshRef.current.material.color.set(
          blendColors(colors.active, colors.overload, blinkIntensity)
        );
      } else if (innerMeshRef.current.material instanceof THREE.MeshBasicMaterial) {
         innerMeshRef.current.material.color.set(colors.active);
      }
    }

    if (shellRef.current) {
      shellRef.current.rotation.y += delta * rotationSpeed * 0.2;
      shellRef.current.rotation.x -= delta * rotationSpeed * 0.1;

      const breatheFreq = overloadState ? 2 : 0.5;
      const breatheAmp = overloadState ? 0.1 : 0.05;
      const breathe = Math.sin(time * breatheFreq) * breatheAmp + 1;
      shellRef.current.scale.set(breathe, breathe, breathe);

      if (shellRef.current.material instanceof THREE.MeshBasicMaterial) {
        shellRef.current.material.opacity = 0.15 * (1 - damageLevel * 0.5);
      }
    }

    if (barrierRef.current) {
      const barrierPulseFreq = overloadState ? 4 : 2;
      const barrierPulseAmp = overloadState ? 0.1 : (0.05 * syncRate);
      const barrierPulse = (Math.sin(time * barrierPulseFreq) * barrierPulseAmp) + 1;

      if (overloadState) {
        barrierRef.current.rotation.y -= delta * 0.3 * (1 + Math.sin(time * 2) * 0.5);
        barrierRef.current.rotation.z += delta * 0.2 * (1 + Math.sin(time * 3) * 0.5);
      } else {
        barrierRef.current.rotation.y -= delta * 0.1;
        barrierRef.current.rotation.z += delta * 0.05;
      }

      barrierRef.current.scale.set(barrierPulse, barrierPulse, barrierPulse); // Apply pulse scale

      // Update Barrier materials based on state
      barrierRef.current.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshBasicMaterial) {
          // Base opacity on connection/sync
          const baseOpacity = isConnected ? (0.4 + Math.sin(time * 3) * 0.1) : 0.1; // Pulsating opacity if connected

          // Reduce opacity on damage
          const damageReduction = 1 - damageLevel * 0.7;

          // More visible in overload state
          const overloadBoost = overloadState ? 1.5 : 1;

          child.material.opacity = baseOpacity * damageReduction * overloadBoost;

          // Update color for overload state
          if (overloadState) {
            const pulseColor = Math.sin(time * 8) * 0.5 + 0.5;
            child.material.color.set(
              blendColors(barrierColor, colors.overload, pulseColor) // Blend with overload color
            );
          } else {
            child.material.color.set(barrierColor); // Use dynamic barrier color
          }
        }
      });

      // Special effect: Barrier hexagon fragments animation
      if (barrierRef.current.children[2]) { // Assuming fragments are the 3rd child group
        barrierRef.current.children[2].children.forEach((fragment, i) => {
          if (fragment instanceof THREE.Mesh) {
            // Normal rotation animation
            fragment.rotation.z += delta * (0.1 + i * 0.01); // Slightly different speed per fragment

            // In overload state or when damaged, make fragments move outward and pulsate
            if (overloadState || damageLevel > 0.5) {
              const distance = 1.5 + (Math.sin(time * 2 + i) * 0.3); // Oscillating distance
              const currentPos = fragment.position.clone().normalize();
              fragment.position.copy(currentPos.multiplyScalar(distance)); // Move outward

              // Pulsate fragments scale
              const scale = 0.4 + Math.sin(time * 8 + i) * 0.2;
              fragment.scale.set(scale, scale, scale);
            } else {
              // Reset position if not overloaded/damaged? Or let them stay distributed?
              // Let's keep them distributed for now. Resetting might look jerky.
            }
          }
        });
      }
    }

    // Hex grid animation (Organization terminal effect)
    if (hexGridRef.current) {
      hexGridRef.current.rotation.y += delta * 0.1; // Slow rotation
      if (hexGridRef.current.material instanceof THREE.LineBasicMaterial) {
        // More visible patterns during critical states
        const isAbnormal = overloadState || powerLevel < 0.3 || damageLevel > 0.5;
        const baseOpacity = isAbnormal ? 0.5 : 0.3;
        hexGridRef.current.material.opacity = baseOpacity + Math.sin(time * 1.5) * 0.1; // Pulsate opacity

        // Change color based on mode
        if (overloadState) {
          hexGridRef.current.material.color.set(colors.overload);
        } else if (damageLevel > 0.5) {
          hexGridRef.current.material.color.set(colors.warning);
        } else {
          hexGridRef.current.material.color.set(colors.hexGrid); // Default hex grid color
        }
      }
    }

    // Data rings rotation (Unit diagnostic UI)
    if (dataRingsRef.current) {
      // Base rotation speed on sync rate - better sync = smoother movement
      const syncEffect = Math.max(0.5, syncRate);
      dataRingsRef.current.rotation.x += delta * 0.1 * syncEffect;
      dataRingsRef.current.rotation.y += delta * 0.15 * syncEffect;

      // Additional ring effects based on states
      dataRingsRef.current.traverse((child) => {
        if (child instanceof THREE.Line && child.material instanceof THREE.LineBasicMaterial) {
          // Normal pulsing opacity
          child.material.opacity = 0.5 + Math.sin(time * (Math.random() + 1)) * 0.2;

          // Damaged rings appear broken/flickering
          if (damageLevel > 0.7) {
            // Randomly hide parts during high damage
            child.material.opacity *= Math.random() > 0.2 ? 1 : 0;
          } else if (damageLevel > 0.4) {
            // Flickering during medium damage
            child.material.opacity *= Math.random() > 0.1 ? 1 : 0.3;
          }

          // Overload state: rings pulse with overload color randomly
          if (overloadState && Math.random() > 0.7) {
            child.material.color.set(colors.overload);
          } else {
             // Reset color if not pulsing overload (ensure it reverts)
             // This needs careful handling if the ring has its own base color
             // Assuming base colors are set initially and only overload pulses override
          }
        }
      });
    }

  // Bio-Fluid animation (like the immersion fluid)
  if (bioFluidRef.current) {
    // Animate Bio-Fluid opacity based on operator vitals
    if (bioFluidRef.current.material instanceof THREE.MeshBasicMaterial) {
      // Base opacity on operator vitals
      const vitalFactor = Math.max(0.2, operatorVitals); // Ensure minimum visibility
      bioFluidRef.current.material.opacity = 0.2 * vitalFactor + Math.sin(time * 0.5) * 0.05; // Gentle pulse

      // Change color based on overload state
      if (overloadState) {
        // Gradually shift to overload color during overload state
        const overloadFactor = Math.sin(time * 3) * 0.5 + 0.5; // Pulsating blend factor
        bioFluidRef.current.material.color.set(
          blendColors(colors.bioFluid, colors.overload, overloadFactor)
        );
      } else {
        bioFluidRef.current.material.color.set(colors.bioFluid); // Reset to default bio-fluid color
      }
    }

    // Gentle swirling motion
    bioFluidRef.current.rotation.y += delta * 0.03;
    bioFluidRef.current.rotation.z += delta * 0.02;
  }

  // Organization terminal/Central System pattern animations
  if (terminalPatternRef.current) {
    // Rotate slowly
    terminalPatternRef.current.rotation.y += delta * 0.05;

    // Special effects based on status
    if (terminalPatternRef.current.material instanceof THREE.MeshBasicMaterial) {
      // More visible during critical events
      const criticalFactor = overloadState || powerLevel < 0.3 ? 0.6 : 0.3;
      terminalPatternRef.current.material.opacity = criticalFactor + Math.sin(time * 2) * 0.1; // Pulsate visibility
    }
  }

  // Anomaly pattern animation
  if (anomalyPatternRef.current) {
    // Gentle rotation for default mode, faster in overload
    const rotationFactor = overloadState ? 0.5 : 0.1;
    anomalyPatternRef.current.rotation.y += delta * rotationFactor;
    anomalyPatternRef.current.rotation.z += delta * rotationFactor * 0.5;

    // Update geometry for flowing/wave effect
    if (anomalyPatternRef.current.geometry instanceof THREE.BufferGeometry) {
      const positions = anomalyPatternRef.current.geometry.getAttribute('position');
      const sizes = anomalyPatternRef.current.geometry.getAttribute('size');

      for (let i = 0; i < positions.count; i++) {
        // Get current position
        const x = positions.getX(i);
        const y = positions.getY(i);
        const z = positions.getZ(i);

        // Calculate distance for wave effect scaling
        const dist = Math.sqrt(x * x + y * y + z * z);
        const normFactor = 1.8 / Math.max(dist, 0.1); // Avoid division by zero

        // Introduce wave effect - more intense in overload
        const waveFreq = overloadState ? 3 : 1.5;
        const waveAmp = overloadState ? 0.2 : 0.05;
        const wave = Math.sin(time * waveFreq + dist * (overloadState ? 5 : 3)) * waveAmp;

        // Apply effect while maintaining general spherical distribution
        // Normalize current position vector before applying wave and scaling
        const posVec = new THREE.Vector3(x, y, z).normalize();
        positions.setXYZ(i,
           posVec.x * (1.8 + wave * dist), // Modulate radius
           posVec.y * (1.8 + wave * dist),
           posVec.z * (1.8 + wave * dist)
        );


        // Pulsate sizes
        const basePulse = Math.sin(time * 2 + i * 0.1) * 0.5 + 1; // Base pulsation
        const sizeBase = Math.random() * 5 + 1; // Random base size
        sizes.setX(i, sizeBase * basePulse * (overloadState ? 1.5 : 1)); // Larger in overload
      }

      positions.needsUpdate = true; // Flag geometry attributes for update
      sizes.needsUpdate = true;
    }

    // Update material parameters
    if (anomalyPatternRef.current.material instanceof THREE.PointsMaterial) {
      anomalyPatternRef.current.material.opacity = isConnected ? 0.7 : 0.3; // Less visible if disconnected

      // More intense size in overload mode
      if (overloadState) {
        // This size is now controlled per-particle via attribute, so global size might not be needed
        // anomalyPatternRef.current.material.size = 3 + Math.sin(time * 8) * 2;
      } else {
        // anomalyPatternRef.current.material.size = 3; // Reset global size if used
      }
       // Ensure sizeAttenuation is true if using vertex sizes
       anomalyPatternRef.current.material.sizeAttenuation = true;
       anomalyPatternRef.current.material.vertexColors = true; // Make sure vertex colors are enabled
    }
  }

  // Damage overlay effect
  if (damageOverlayRef.current && damageLevel > 0) {
    if (damageOverlayRef.current.material instanceof THREE.MeshBasicMaterial) {
      // Set opacity based on damage level, with pulsing
      const baseOpacity = damageLevel * 0.3;
      const pulseOpacity = Math.sin(time * 2) * 0.1;
      damageOverlayRef.current.material.opacity = baseOpacity + pulseOpacity;

      // More intense red (overload color) in overload state
      if (overloadState) {
        damageOverlayRef.current.material.color.set(colors.overload);
      } else {
         damageOverlayRef.current.material.color.set(colors.critical); // Default to critical color for damage
      }
    }
  }

  // Organization logo animation
  if (organizationLogoRef.current) {
    // Gentle floating motion
    organizationLogoRef.current.position.y = Math.sin(time * 0.5) * 0.05;
    organizationLogoRef.current.rotation.y += delta * 0.1; // Slow rotation

    // Opacity based on connection status
    organizationLogoRef.current.traverse((child) => {
      if (child instanceof THREE.Line && child.material instanceof THREE.LineBasicMaterial) {
        child.material.opacity = isConnected ? 0.8 : 0.3; // Fade if disconnected
      }
    });
  }

  // Sound wave visualization animation
  if (soundWaveRef.current) {
    if (soundWaveRef.current.geometry instanceof THREE.PlaneGeometry) {
      const positions = soundWaveRef.current.geometry.attributes.position.array;

      // Animate wave pattern based on status - more frantic in overload
      const frequency = overloadState ? 15 : 5;
      const amplitude = overloadState ? 0.1 : 0.05;

      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        // y = sin(time * freq + x * spatial_freq) * amplitude
        positions[i + 1] = Math.sin(time * frequency + x * 5) * amplitude;
      }

      soundWaveRef.current.geometry.attributes.position.needsUpdate = true; // Flag for update
    }

    // Hide when disconnected
    if (soundWaveRef.current.material instanceof THREE.MeshBasicMaterial) {
      soundWaveRef.current.material.opacity = isConnected ? 0.7 : 0;

      // Color changes with status
      if (overloadState) {
        soundWaveRef.current.material.color.set(colors.overload);
      } else if (powerLevel < 0.3) {
        soundWaveRef.current.material.color.set(colors.warning);
      } else {
        soundWaveRef.current.material.color.set(colors.unit); // Default to unit color
      }
    }
  }

  // Glitch effect (like when Unit sync destabilizes)
  glitchTimerRef.current += delta;
  if (glitchIntensity > 0) {
    if (Math.random() < glitchIntensity) { // Trigger glitch based on intensity
      // Random position jitter
      const jitterAmount = glitchIntensity * 0.1;
      groupRef.current.position.set(
        (Math.random() - 0.5) * jitterAmount,
        (Math.random() - 0.5) * jitterAmount,
        (Math.random() - 0.5) * jitterAmount
      );

      // Random scale glitch (less frequent)
      if (Math.random() < 0.1) {
        const scaleGlitch = 1 + (Math.random() - 0.5) * glitchIntensity;
        groupRef.current.scale.set(scaleGlitch, scaleGlitch, scaleGlitch);
      }

      // Random color shifts (e.g., inner core flashes overload color)
      if (Math.random() < 0.05 && overloadState) {
        if (innerMeshRef.current && innerMeshRef.current.material instanceof THREE.MeshBasicMaterial) {
          innerMeshRef.current.material.color.set(colors.overload);
          // Consider setting a timer to revert the color after a short duration
        }
      }
    } else {
      // Reset position and scale when not actively glitching
      groupRef.current.position.set(0, 0, 0);
      groupRef.current.scale.set(1, 1, 1);
      // Ensure colors reset if changed during glitch (might need more robust state)
    }
  }

  // Performance optimization - signal regression if needed and skip some updates
  state.performance.regress(); // Signal potential regression
  // Skip some updates randomly on lower performance to save resources
  if (Math.random() > 0.7) return; 
});

// Abstracted shaders for the anomaly pattern (dramatic energy effects)
const anomalyPointsShader = useMemo(() => {
return {
  vertexShader: `
    attribute float size; // Size per particle
    attribute vec3 color; // Color per particle
    varying vec3 vColor; // Pass color to fragment shader

    void main() {
      vColor = color; // Pass vertex color through
      vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
      // Calculate point size based on distance and attribute
      gl_PointSize = size * (300.0 / -mvPosition.z);
      gl_Position = projectionMatrix * mvPosition;
    }
  `,
  fragmentShader: `
    varying vec3 vColor; // Receive color from vertex shader

    void main() {
      // Create geometric patterns within the point (e.g., hexagonal)
      float r = distance(gl_PointCoord, vec2(0.5, 0.5)); // Distance from center

      // Hexagonal shape mask
      float a = atan(gl_PointCoord.y - 0.5, gl_PointCoord.x - 0.5); // Angle
      float f = cos(a * 6.0) * 0.2 + 0.8; // Modulate radius for hex shape

      if (r > 0.5 * f) discard; // Discard fragments outside the hex shape

      // Glow effect (brighter center, fading edges)
      vec3 color = vColor; // Use the particle's color
      // Smooth fade from center to edge based on modulated radius 'f'
      float alpha = 1.0 - smoothstep(0.3 * f, 0.5 * f, r);

      gl_FragColor = vec4(color, alpha); // Output final color and alpha
    }
  `
};
}, []);

// Create geometry for anomaly pattern particles
const anomalyGeometry = useMemo(() => {
    const geo = new THREE.BufferGeometry();
    const particleCount = 500; // Number of particles

    const positions = new Float32Array(particleCount * 3);
    const colorsAttr = new Float32Array(particleCount * 3); // Renamed from 'colors' to avoid conflict
    const sizes = new Float32Array(particleCount);

    const baseColor = new THREE.Color(colors.anomaly); // Use anomaly color

    for (let i = 0; i < particleCount; i++) {
      const idx = i * 3;

      // Distribute particles spherically
      const phi = Math.acos(-1 + (2 * i) / particleCount);
      const theta = Math.sqrt(particleCount * Math.PI) * phi;
      const radius = 1.8; // Base radius

      positions[idx] = radius * Math.sin(phi) * Math.cos(theta);
      positions[idx + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[idx + 2] = radius * Math.cos(phi);

      // Assign color with slight variations
      colorsAttr[idx] = baseColor.r * (0.8 + Math.random() * 0.4);
      colorsAttr[idx + 1] = baseColor.g * (0.8 + Math.random() * 0.4);
      colorsAttr[idx + 2] = baseColor.b * (0.8 + Math.random() * 0.4);

      // Assign random base size
      sizes[i] = Math.random() * 5 + 1;
    }

    geo.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geo.setAttribute('color', new THREE.BufferAttribute(colorsAttr, 3)); // Use renamed variable
    geo.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    return geo;
}, [colors.anomaly]); // Depends on anomaly color


return (
<group ref={groupRef}>
  
  <group ref={dataRingsRef} />

  
  <lineSegments ref={hexGridRef}>
    <primitive object={hexGridGeometry} attach="geometry" />
    <lineBasicMaterial
      color={colors.hexGrid}
      transparent={true}
      opacity={0.3}
    />
  </lineSegments>

  
   <points ref={anomalyPatternRef}>
      <primitive object={anomalyGeometry} attach="geometry" />
      <shaderMaterial
        args={[anomalyPointsShader]}
        transparent={true}
        depthWrite={false}
        vertexColors={true} // Enable vertex colors
        // No need for size/sizeAttenuation here if using attributes + shader logic
      />
    </points>


  
  <mesh ref={terminalPatternRef} scale={1.7} rotation={[0, 0, Math.PI / 6]}>
    <sphereGeometry args={[1, 32, 24]} />
    <meshBasicMaterial
      map={createTerminalPatternTexture} // Use the generated texture
      transparent={true}
      opacity={0.3}
      side={THREE.BackSide} // Render on the inside
    />
  </mesh>

  
  <group ref={barrierRef} scale={1.7} />

  
  <mesh ref={bioFluidRef} scale={1.2}>
    <sphereGeometry args={[1, 32, 24]} />
    <meshBasicMaterial
      color={colors.bioFluid} // Use bio-fluid color
      transparent={true}
      opacity={0.2}
      depthWrite={false} // Don't obscure elements behind it
    />
  </mesh>

  
  <mesh ref={shellRef} scale={1.6}>
    <sphereGeometry args={[1, 32, 24]} />
    <meshBasicMaterial
      color={primaryColor} // Use dynamic primary color
      transparent={true}
      opacity={0.15}
      side={THREE.BackSide} // Render on the inside
    />
  </mesh>

  
  <mesh ref={meshRef} scale={1.5}>
    <dodecahedronGeometry args={[1, 2]} /> 
    <meshStandardMaterial
      color={primaryColor}
      wireframe={true}
      emissiveIntensity={0.5} // Base emission
      emissive={primaryColor} // Emits its own color
    />
  </mesh>

  
  <mesh ref={innerMeshRef} scale={0.45}>
    <icosahedronGeometry args={[1, 1]} /> 
    <meshBasicMaterial
      color={colors.active} // Use active color (often unit-specific)
      wireframe={true}
      transparent={true}
      opacity={0.9}
    />
  </mesh>

  
  <mesh scale={0.2}>
    <sphereGeometry args={[1, 16, 16]} />
    <meshStandardMaterial
      color={primaryColor}
      emissive={primaryColor}
      emissiveIntensity={1.0} // Strong emission
    />
  </mesh>

  
  <mesh ref={soundWaveRef} position={[0, -1.2, 0]} rotation={[Math.PI / 2, 0, 0]}>
     
     <primitive object={createSoundWave} />
  </mesh>


  
  <mesh ref={damageOverlayRef} scale={1.9}>
    <sphereGeometry args={[1, 32, 24]} />
    <meshBasicMaterial
      color={colors.critical} // Default to critical color
      transparent={true}
      opacity={0} // Start invisible
      blending={THREE.AdditiveBlending} // Additive blend for glow effect
    />
  </mesh>

  
  <group ref={organizationLogoRef} position={[0, 1.2, 0]} />
</group>
);
}

// Helper function for blending particle effects (like Barrier dissolve)
export function BarrierDissolveEffect({ // Renamed from ATFieldDissolveEffect
position = [0, 0, 0],
color = '#FFA500', // Default to barrier color base
size = 1.0,
duration = 2.0,
onComplete = () => {},
}: {
position?: [number, number, number],
color?: string,
size?: number,
duration?: number,
onComplete?: () => void,
}) {
const { clock } = useThree();
const particlesRef = useRef<THREE.Points>(null!);
const startTimeRef = useRef(clock.getElapsedTime());
const [completed, setCompleted] = useState(false);

// Particle geometry for Barrier dissolve effect
const dissolveGeometry = useMemo(() => {
const geo = new THREE.BufferGeometry();
const particleCount = 1000; // Number of particles

  const positions = new Float32Array(particleCount * 3);
  const colorsAttr = new Float32Array(particleCount * 3); // Renamed
  const sizes = new Float32Array(particleCount);

  const colorObj = new THREE.Color(color);

  for (let i = 0; i < particleCount; i++) {
    const idx = i * 3;

    // Create spherical distribution (could be hexagonal if needed)
    const theta = Math.random() * Math.PI * 2;
    const phi = Math.acos(2 * Math.random() - 1); // Uniform spherical distribution
    const radius = Math.random() * size; // Random radius within size

    positions[idx] = Math.sin(phi) * Math.cos(theta) * radius;
    positions[idx + 1] = Math.sin(phi) * Math.sin(theta) * radius;
    positions[idx + 2] = Math.cos(phi) * radius;

    // Color with slight variation
    colorsAttr[idx] = colorObj.r * (0.8 + Math.random() * 0.4);
    colorsAttr[idx + 1] = colorObj.g * (0.8 + Math.random() * 0.4);
    colorsAttr[idx + 2] = colorObj.b * (0.8 + Math.random() * 0.4);

    // Varied particle sizes
    sizes[i] = Math.random() * 5 + 2;
  }

  geo.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  geo.setAttribute('color', new THREE.BufferAttribute(colorsAttr, 3)); // Use renamed variable
  geo.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

  return geo;
}, [color, size]); // Dependencies

// Animation for dissolve effect
useFrame((state) => {
  if (completed) return; // Stop animation when done

const elapsedTime = state.clock.getElapsedTime() - startTimeRef.current;
const progress = Math.min(elapsedTime / duration, 1.0);

if (particlesRef.current) {
  // Expand outward based on progress
  const scale = 1.0 + progress * 2.0;
  particlesRef.current.scale.set(scale, scale, scale);
  
  // Fade out over time
  if (particlesRef.current.material instanceof THREE.PointsMaterial) {
    particlesRef.current.material.opacity = 1.0 - progress;
  }
  
  // Animation completed
  if (progress >= 1.0) {
    setCompleted(true);
    onComplete();
  }
}
});

if (completed) return null;

return (
<points ref={particlesRef} position={position}>
  <primitive object={dissolveGeometry} attach="geometry" />
  <pointsMaterial
    // size={3} // Base size removed - controlled by attribute/shader
    transparent={true}
    vertexColors={true} // Use colors from geometry attribute
    depthWrite={false}
    sizeAttenuation={true} // Make particles smaller further away
    blending={THREE.AdditiveBlending} // Additive blend for glow
  />
</points>
);
}

// Utility component for System countdown timer display
export function SystemCountdownTimer({ // Renamed from EvaCountdownTimer
timeRemaining = 5.0, // Time in minutes
isEmergency = false, // Flag for emergency state
}: {
timeRemaining: number,
isEmergency?: boolean,
}) {
const { clock } = useThree();
// const textRef = useRef<THREE.Mesh>(null!); // Ref not directly applicable to <Text>
const [displayTime, setDisplayTime] = useState('05:00');
const [opacity, setOpacity] = useState(0.8); // State for opacity animation

// Font URL - replace with your actual font path if needed
const fontUrl = '/fonts/Orbitron-VariableFont_wght.ttf'; // Example path

// Update timer display and opacity
useFrame(() => {
  const minutes = Math.floor(timeRemaining);
  const seconds = Math.floor((timeRemaining - minutes) * 60);

  const newDisplayTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  if (newDisplayTime !== displayTime) {
    setDisplayTime(newDisplayTime);
  }

  // Pulse effect for emergency
  if (isEmergency) {
    const pulse = Math.sin(clock.getElapsedTime() * 5) * 0.5 + 0.5; // 0 to 1 oscillation
    setOpacity(0.5 + pulse * 0.5); // Modulate opacity between 0.5 and 1.0
  } else {
    setOpacity(0.8); // Reset opacity if not emergency
  }
});

// FIX: Replace textGeometry with drei/Text
return (
  <Text
    // ref={textRef} // Ref works differently with Text, manage state instead
    position={[0, 0, 0]} // Adjust position as needed
    scale={0.1} // Adjust scale as needed
    color={isEmergency ? '#FF0000' : '#00FFFF'} // Dynamic color
    fontSize={10} // Adjust font size (relative to scale)
    font={fontUrl} // Path to font file
    anchorX="center" // Center text horizontally
    anchorY="middle" // Center text vertically
    material-transparent={true} // Ensure material is transparent
    material-opacity={opacity} // Apply animated opacity
  >
    {displayTime}
  </Text>
);
}


// Specialized version for Unit Interface System (abstracted from Entry Plug)
export function InterfaceSystem({ // Renamed from EntryPlugSystem
isEngaged = false, // Renamed from isInserted
syncRate = 0.78, // Renamed from syncRatio
lifeSupportLevel = 0.95, // Renamed from oxygenLevel
operatorVitals = 0.85, // Renamed from pilotVitals
unitId = 'TestType', // Renamed from evaUnit
}: {
isEngaged?: boolean,
syncRate?: number,
lifeSupportLevel?: number,
operatorVitals?: number,
unitId?: string,
}) {
// Component implementation for interface system specific display
// This interfaces with the core model, passing relevant parameters

// Utilize the core model with interface system specific parameters
return (
<group>
  <CoreModel
    powerLevel={lifeSupportLevel} // Map life support to power level for visualization
    powerStatus={isEngaged ? 'ACTIVE' : 'STANDBY'} // Map engagement to power status
    isConnected={isEngaged} // Connection status based on engagement
    syncRate={syncRate} // Pass sync rate
    overloadState={syncRate > 1.5} // Determine overload state based on sync rate threshold
    operatorVitals={operatorVitals} // Pass operator vitals
    unitId={unitId} // Pass unit ID
    // Damage level could be passed from a higher state if applicable
  />

  
  
</group>
);
}
