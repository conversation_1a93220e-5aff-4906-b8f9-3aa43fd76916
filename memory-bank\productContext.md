# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-04 00:20:33 - Log of updates made will be appended as footnotes to the end of this file.
2025-06-04 00:24:33 - Updated with details from `package.json`, `app.json`, `tailwind.config.js`, and `tsconfig.json`.

*

## Project Goal

*   Create a visually striking system monitoring application with cyberpunk aesthetics that displays real-time system metrics including network performance, power usage, and storage status. The application is named "Aegis System Monitor".

## Key Features

*   Real-time network monitoring with speed tests, utilizing `@react-native-community/netinfo`.
*   Power consumption analysis and battery health tracking, using `expo-battery`.
*   Storage capacity visualization, potentially using `expo-file-system` and `react-native-device-info`.
*   Radar-style intrusion detection visualization.
*   Cyberpunk-inspired UI with glitch effects and neon styling, leveraging `nativewind` (Tailwind CSS v4), custom `aegis-orange`, `aegis-red`, `aegis-cyan` colors, and `Orbitron-Bold` and `SpaceMono-Regular` fonts with text shadows.
*   3D visualizations using `Three.js` and `@react-three/fiber`, `@react-three/drei`.
*   Haptic feedback using `expo-haptics`.
*   Notifications using `expo-notifications`.
*   Google Mobile Ads integration (`react-native-google-mobile-ads`).

## Overall Architecture

*   React Native application built with Expo (`expo`, `expo-router` with `typedRoutes` enabled).
*   New React Native Architecture (`newArchEnabled: true`).
*   State management using Zustand (`zustand`).
*   Navigation using `@react-navigation/native`, `@react-navigation/bottom-tabs`, `@react-navigation/native-stack`.
*   Styling with NativeWind (`nativewind`, `tailwindcss`).
*   3D rendering with `expo-gl`, `three`, `@react-three/fiber`, `@react-three/drei`.
*   Native system APIs for metric collection (`expo-battery`, `expo-device`, `@react-native-community/netinfo`, `react-native-device-info`).
*   TypeScript (`typescript`) with strict mode and path aliases (`@/*`).
*   Build properties managed by `expo-build-properties`.
*   Splash screen configured with `expo-splash-screen`.
*   Font management with `expo-font`.
*   Web support with `react-native-web`.
*   WebView support with `react-native-webview`.
*   Blur effects with `expo-blur`.
*   Brightness control with `expo-brightness`.
*   Linear gradients with `expo-linear-gradient`.
*   System UI control with `expo-system-ui`.
*   Expo Symbols for icons.
*   Async Storage for data persistence.