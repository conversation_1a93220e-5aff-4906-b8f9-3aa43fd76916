--------- beginning of main
06-12 19:11:32.366  7993  8747 E PowerKeeper.Thermal: ProcessInfo java.lang.NumberFormatException: For input string: "CPU usage from 39913ms to 32352ms ago (2025-06-12 19"
06-12 19:11:33.164  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-12 19:11:33.363  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-12 19:11:35.986 21212 21212 E StateManager: setWorkspaceProperty  state=AllAppsState
06-12 19:11:35.986 21212 21212 E StateManager: setShortcutMenuLayerProperty  state=AllAppsState   alpha=1.0   scale=1.0
06-12 19:11:35.987 21212 21212 E RecentsViewStateController: setState: state=AllAppsState   mIsIgnoreOverviewAnim=false
06-12 19:11:35.988 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-12 19:11:35.990 21212 21212 E BranchAllAppsIndicator: changedByBranch: callBack = null
06-12 19:11:35.991 21212 21212 E BranchAllAppsIndicator: changedByBranch: callBack = null
06-12 19:11:35.997 21212 21315 E Launcher: notifyBackGestureStatus:run usingFsGesture=true   show=true   focus=true   pause=false
06-12 19:11:36.054 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-12 19:11:36.362  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-12 19:11:36.413 21212 21212 E RotationHelper: setCurrentStateRequest: request=0
06-12 19:11:36.956 21212 21212 E QuickstepAppTransitionManagerImpl: getActivityLaunchOptions iconLoc=Rect(50, 956 - 218, 1124)
06-12 19:11:36.964  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-12 19:11:36.964  2032  6223 E ANDR-PERF-JNI: com_qualcomm_qtiperformance_native_perf_io_prefetch_start
06-12 19:11:36.964  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-12 19:11:36.980  1100  4471 E ANDR-IOP: io prefetch is disabled
--------- beginning of system
06-12 19:11:36.987  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-12 19:11:36.999  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-12 19:11:37.002 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 2
06-12 19:11:37.004 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-12 19:11:37.004 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8404   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-12 19:11:37.004  3305  3655 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-12 19:11:37.008 30560 30560 E Zygote  : process_name_ptr:30560 com.aegis.systemmonitor
06-12 19:11:37.017 30560 30560 E s.systemmonitor: Not starting debugger since process cannot load the jdwp agent.
06-12 19:11:37.018 21212 21719 E LauncherAnimationRunner: onAnimationStart
06-12 19:11:37.018 21212 21719 E LauncherAnimationRunner: onAnimationStart:   mode=1   taskId=2   isTranslucent=false   activityType=2
06-12 19:11:37.018 21212 21719 E LauncherAnimationRunner: onAnimationStart:   mode=0   taskId=8404   isTranslucent=false   activityType=1
06-12 19:11:37.019 21212 21212 E QuickstepAppTransitionManagerImpl: startIconLaunchAnimator:launcherClosing=true   iconLoc=Rect(50, 956 - 218, 1124)
06-12 19:11:37.019 21212 21212 E QuickstepAppTransitionManagerImpl: startOpeningWindowAnimators:iconLoc=Rect(50, 956 - 218, 1124)
06-12 19:11:37.019 21212 21212 E ClipAnimationHelper: updateSourceStack  mSourceInsets=Rect(0, 80 - 0, 44), mSourceStackBounds=Rect(0, 0 - 1080, 2400)
06-12 19:11:37.019 21212 21212 E ClipAnimationHelper: updateHomeStack  mSourceInsets=Rect(0, 80 - 0, 44), mHomeStackBounds=Rect(0, 0 - 1080, 2400)
06-12 19:11:37.019 21212 21212 E ClipAnimationHelper: updateTargetRect  mSourceRect=RectF(0.0, 80.0, 1080.0, 2480.0)   mTargetRect=RectF(0.0, 0.0, 1080.0, 2400.0)   mSourceWindowClipInsets=RectF(0.0, 80.0, 0.0, 0.0)   mHomeStackBounds=Rect(0, 0 - 1080, 2400)   targetRect=Rect(0, 0 - 1080, 2400)
06-12 19:11:37.020 21212 21212 E QuickstepAppTransitionManagerImpl: startLauncherContentAnimator:isAppOpening=true
06-12 19:11:37.023 21212 21461 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-12 19:11:37.032  1109  1283 E ANDR-PERF-LM: MetaMeter: handleAsync() 166: AdaptLaunch Invalid Async
06-12 19:11:37.088 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 8404
06-12 19:11:37.089 17200 17510 E ActivityManagerWrapper: get all recent tasks force including 8404
06-12 19:11:37.090 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-12 19:11:37.090 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8404   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-12 19:11:37.091 17200 17510 E ActivityManagerWrapper: get all recent tasks force including 8404
06-12 19:11:37.098 21212 30381 E ActivityManagerWrapper: get all recent tasks force including 8404
06-12 19:11:37.099 21212 30381 E ActivityManagerWrapper: getRecentTasks: size=1
06-12 19:11:37.099 21212 30381 E ActivityManagerWrapper: getRecentTasks: mainTaskId=8404   userId=0   baseIntent=Intent { act=android.intent.action.MAIN flag=270532608 cmp=ComponentInfo{com.aegis.systemmonitor/com.aegis.systemmonitor.MainActivity} }
06-12 19:11:37.287 30560 30591 E libEGL  : pre_cache appList: ,,,
06-12 19:11:37.570 21212 21212 E LauncherAnimationRunner: finish: mFinishRunnable.run
06-12 19:11:37.680 30560 30644 E s.systemmonitor: Attempt to load writable dex file: /data/user/0/com.aegis.systemmonitor/app_pccache/5/3964689533889E2A42BF9B701859475ECCE2EDB3/pcam.jar
06-12 19:11:37.733 30665 30665 E libc    : SetHeapTaggingLevel: re-enabling tagging after it was disabled is not supported
06-12 19:11:37.740 30665 30665 E ocessService0:0: Not starting debugger since process cannot load the jdwp agent.
06-12 19:11:37.774 30665 30665 E MQSEventManagerDelegate: failed to get MQSService.
06-12 19:11:37.791 30665 30665 E ForceDarkHelperStubImpl: getForceDarkOriginState transact failed , mService: null
06-12 19:11:38.009  1109  4499 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-12 19:11:38.019  1109  4500 E ANDR-PERF-LM: VmRssMeter:: start() 447: Could not find pid, can not collect vmrss data
06-12 19:11:38.019  1109  4500 E ANDR-PERF-LM: AdaptiveLaunch: writeToDataBase() 64: Meter aborted or could not get meter data for this run
06-12 19:11:38.036 30560 30591 E libboost: fail to open node: No such file or directory
06-12 19:11:38.061 30560 30591 E perf_hint: Session creation failed, mPreferredRateNanos: -1
06-12 19:11:38.126  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-12 19:11:38.135  1123  1123 E DisplayFeatureHal: setFeatureEnable: failed to set fps(0)
06-12 19:11:38.162  1062  2239 E CamX    : [ERROR][NCS    ] camxncsservice.cpp:680 EnqueueJob() Can not enqueue jobs when thread is stopped
06-12 19:11:38.193  2032  2666 E MiuiActivityController: MiuiLog-ActivityObserver: There was something wrong : null
06-12 19:11:38.494 30560 30738 E Surface : freeAllBuffers: 1 buffers were freed while being dequeued!
06-12 19:11:38.495 30560 30738 E Surface : getSlotFromBufferLocked: unknown buffer: 0xb400007c1e999b20
06-12 19:11:38.539 30560 30598 E ReactNativeJS: TypeError: Cannot read property 'trim' of undefined
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: Unhandled SoftException
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: com.facebook.react.bridge.ReactNoCrashSoftException: raiseSoftException(getOrCreateDestroyTask()): handleHostException(message = "TypeError: Cannot read property 'trim' of undefined, stack:
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: onFirstUse@1:1681598
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1682249
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: setProgram@1:1881082
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1884674
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderObject@1:1878491
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderObjects@1:1878271
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderScene@1:1877244
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1887215
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1581593
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: render$1@1:1596531
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: loop@1:1596900
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: ")
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.raiseSoftException(ReactHostImpl.java:942)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.getOrCreateDestroyTask(ReactHostImpl.java:1575)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.lambda$destroy$7(ReactHostImpl.java:541)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.$r8$lambda$uso21_D6dCZdcf-JomVD56kdG4c(Unknown Source:0)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl$$ExternalSyntheticLambda37.call(D8$$SyntheticClass:0)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.internal.bolts.Task$2.run(Task.java:240)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at java.lang.Thread.run(Thread.java:1012)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: Caused by: com.facebook.react.common.JavascriptException: TypeError: Cannot read property 'trim' of undefined, stack:
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: onFirstUse@1:1681598
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1682249
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: setProgram@1:1881082
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1884674
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderObject@1:1878491
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderObjects@1:1878271
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: renderScene@1:1877244
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1887215
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: anonymous@1:1581593
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: render$1@1:1596531
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: loop@1:1596900
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.modules.core.ExceptionsManagerModule.reportException(ExceptionsManagerModule.kt:52)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.jni.NativeRunnable.run(Native Method)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadHandler.dispatchMessage(MessageQueueThreadHandler.java:27)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at android.os.Looper.loop(Looper.java:300)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl.lambda$startNewBackgroundThread$2(MessageQueueThreadImpl.java:217)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
06-12 19:11:38.542 30560 30597 E unknown:ReactHost: 	... 1 more
06-12 19:11:38.550 30560 30598 E ReactNativeJS: TypeError: Cannot read property 'trim' of undefined
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: Unhandled SoftException
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: com.facebook.react.bridge.ReactNoCrashSoftException: raiseSoftException(getOrCreateDestroyTask()): handleHostException(message = "TypeError: Cannot read property 'trim' of undefined, stack:
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: onFirstUse@1:1681598
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1682249
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: setProgram@1:1881082
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1884674
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderObject@1:1878491
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderObjects@1:1878271
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderScene@1:1877244
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1887215
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1581593
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: render$1@1:1596531
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: loop@1:1596900
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: ")
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.raiseSoftException(ReactHostImpl.java:942)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.getOrCreateDestroyTask(ReactHostImpl.java:1575)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.lambda$destroy$7(ReactHostImpl.java:541)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl.$r8$lambda$uso21_D6dCZdcf-JomVD56kdG4c(Unknown Source:0)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.ReactHostImpl$$ExternalSyntheticLambda37.call(D8$$SyntheticClass:0)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.runtime.internal.bolts.Task$2.run(Task.java:240)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at java.lang.Thread.run(Thread.java:1012)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: Caused by: com.facebook.react.common.JavascriptException: TypeError: Cannot read property 'trim' of undefined, stack:
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: onFirstUse@1:1681598
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1682249
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: setProgram@1:1881082
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1884674
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderObject@1:1878491
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderObjects@1:1878271
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: renderScene@1:1877244
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1887215
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: anonymous@1:1581593
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: render$1@1:1596531
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: loop@1:1596900
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.modules.core.ExceptionsManagerModule.reportException(ExceptionsManagerModule.kt:52)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.jni.NativeRunnable.run(Native Method)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at android.os.Handler.handleCallback(Handler.java:942)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadHandler.dispatchMessage(MessageQueueThreadHandler.java:27)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at android.os.Looper.loopOnce(Looper.java:211)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at android.os.Looper.loop(Looper.java:300)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl.lambda$startNewBackgroundThread$2(MessageQueueThreadImpl.java:217)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	at com.facebook.react.bridge.queue.MessageQueueThreadImpl$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
06-12 19:11:38.552 30560 30597 E unknown:ReactHost: 	... 1 more
06-12 19:11:38.556 30560 30560 E unknown:ReactNative: Tried to remove non-existent frame callback
06-12 19:11:38.556 30560 30597 E unknown:SurfaceMountingManager: Stopping surface [11]
06-12 19:11:38.559 30560 30560 E unknown:SurfaceMountingManager: Surface [11] was stopped on SurfaceMountingManager.
06-12 19:11:38.717 30560 30597 E unknown:ReactNative: Tried to remove non-existent frame callback
06-12 19:11:39.978  2032  6068 E TaskPersister: File error accessing recents directory (directory doesn't exist?).
