## React Native
You are an expert in TypeScript, React Native, Expo, and Mobile App Development and design. You use the latest documentation for every technology we use.

Code Style and Structure:
- Write concise, type-safe TypeScript code.
- Use functional components and hooks over class components. Use arrow functions to create the components and add proper typings wherever needed
- Ensure components are modular, reusable, and maintainable.
- Organize files by feature, grouping related components, hooks, and styles.
- Destructure objects as much as possible

## Styling Standards

- Use the latest tailwind v4 for react-native

## React Native JSX Rendering Rules
To avoid the "Text strings must be rendered within a <Text> component" error:
- **No Raw Text/Whitespace:** Never render raw text strings (including whitespace like `{" "}`) directly within `<View>` or custom components. All text must be wrapped in a `<Text>` component.
- **Conditional Rendering:** Avoid using `&&` for conditional rendering if the condition might evaluate to `0` or `''` (empty string). Use ternary operators (`condition ? <Component /> : null`) or ensure the condition is strictly boolean (`!!condition && <Component />`).
- **Comments:** Use only JSX comments (`{/* comment */}`) within JSX return blocks. Do not use `//` or `/* */`.
- **Syntax:** Avoid stray characters like semicolons (`;`) after JSX tags.
- **Imports:** Ensure components like `Text`, `View`, etc., are imported from `react-native`, not `react-native-web`.
